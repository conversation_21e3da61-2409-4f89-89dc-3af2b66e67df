<?php

use Drupal\webform\Entity\WebformSubmission;

/**
 * Implements hook_webform_submission_insert().
 */
function webform_docx_mailer_webform_submission_insert(WebformSubmission $webform_submission)
{
  $webform = $webform_submission->getWebform();

  // Vérifier si c'est le bon webform
  if ($webform->id() === 'webform_docx_mailer') {
    webform_docx_mailer_send_email($webform_submission);
  }
}

/**
 * Envoie un email avec le fichier DOCX en pièce jointe.
 */
function webform_docx_mailer_send_email(WebformSubmission $submission)
{
  $data = $submission->getData();

  // Récupérer l'adresse email du formulaire
  $email = $data['email_address'] ?? '';
  $first_name = $data['first_name_s'] ?? '';
  $last_name = $data['last_name'] ?? '';

  // Validation de l'email
  if (empty($email)) {
    \Drupal::logger('webform_docx_mailer')->error('No email address provided in the form.');
    return;
  }

  // Le fichier DOCX dans le répertoire public
  $file_path = 'public://creamakerbasic.docx';
  $file_system = \Drupal::service('file_system');
  $real_path = $file_system->realpath($file_path);

  if (!$real_path || !file_exists($real_path)) {
    \Drupal::logger('webform_docx_mailer')->error('DOCX file not found: ' . $file_path);
    return;
  }

  // Use traditional mail system with proper attachment handling
  $params = [
    'subject' => 'Your CreaMAKER Basic Document',
    'message' => "Hello $first_name $last_name,\n\nThank you for your submission. Please find your CreaMAKER Basic document attached.\n\nBest regards,\nThe CreaMAKER Team",
    'attachments' => [
      [
        'filepath' => $real_path,
        'filename' => 'creamakerbasic.docx',
        'filemime' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ]
    ],
  ];

  try {
    $mail_manager = \Drupal::service('plugin.manager.mail');
    $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();

    $result = $mail_manager->mail('webform_docx_mailer', 'submission_docx', $email, $langcode, $params, NULL, TRUE);

    if (!$result['result']) {
      \Drupal::logger('webform_docx_mailer')->error('Failed to send email with attachment to: ' . $email);
    } else {
      \Drupal::logger('webform_docx_mailer')->info('Email with DOCX attachment sent successfully to: ' . $email);
    }
  } catch (\Exception $e) {
    \Drupal::logger('webform_docx_mailer')->error('Error sending email: ' . $e->getMessage());
  }
}

/**
 * Implements hook_mail().
 */
function webform_docx_mailer_mail($key, &$message, $params)
{
  if ($key === 'submission_docx') {
    $message['subject'] = $params['subject'];
    $message['body'][] = $params['message'];

    // Add attachments - copy from params to message params for Symfony Mailer
    if (!empty($params['attachments'])) {
      $message['params']['attachments'] = $params['attachments'];
    }
  }
}



/**
 * Implements hook_theme().
 */
function webform_docx_mailer_theme($existing, $type, $theme, $path)
{
  return [
    'webform_docx_mailer_modal' => [
      'variables' => [
        'form' => NULL,
      ],
      'template' => 'webform-docx-mailer-modal',
    ],
  ];
}

/**
 * Prepares variables for webform DOCX mailer modal template.
 */
function template_preprocess_webform_docx_mailer_modal(&$variables)
{
  // Ensure the form is properly rendered
  if (isset($variables['form']) && is_array($variables['form'])) {
    $variables['form']['#attached']['library'][] = 'webform_docx_mailer/webform_docx_mailer';
  }
}

/**
 * Implements hook_page_attachments().
 */
function webform_docx_mailer_page_attachments(array &$attachments)
{
  // Add library to all pages to ensure modal functionality works
  $route_name = \Drupal::routeMatch()->getRouteName();
  if (
    strpos($route_name, 'webform_docx_mailer') !== FALSE ||
    strpos($route_name, 'entity.webform.canonical') !== FALSE
  ) {
    $attachments['#attached']['library'][] = 'webform_docx_mailer/webform_docx_mailer';
  }
}

/**
 * Implements hook_form_alter().
 */
function webform_docx_mailer_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id)
{
  // Cibler uniquement le formulaire de soumission du webform 'webform_docx_mailer'
  if (strpos($form_id, 'webform_submission_webform_docx_mailer') === 0) {

    // Ajouter du texte HTML en haut du formulaire (header)
    $form['custom_intro_text'] = [
      '#type' => 'markup',
      '#markup' => '<div class="webform-custom-header text-center"><h2 class="display-4" style="color:blue;">
BASIC CreaMAKER </h2><p class="display-6" style="color:blue;">Delivery Form.</p><p>Get my BASIC CreaMAKER tutorial</p> <p class="text-start">To receive your BASIC CreaMAKER tutorial by email within the next seconds, please:</p><br>
<ul class="list-unstyled text-start"><li>1. Fill in the fields below</li>
<li>2. Check the box "I wish to receive the CreaFREE newsletter"</li>
<li>3. Click the white on blue Send button below</li></ul></div>',
      '#weight' => -100, // S'assurer qu'il s'affiche en haut
    ];

    // Joindre une bibliothèque JS ou CSS si besoin
    $form['#attached']['library'][] = 'webform_docx_mailer/webform_docx_mailer';
  }
}
