/**
 * @file
 * Styles for webform DOCX mailer.
 */

.creamaker-basic-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.webform-custom-header {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid #dee2e6;
}

.webform-custom-header h2 {
  color: #007bff;
  font-weight: bold;
  margin-bottom: 10px;
}

.webform-custom-header .display-6 {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 15px;
}

.webform-custom-header .lead {
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.webform-custom-header ul {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.webform-custom-header li {
  padding: 5px 0;
  font-size: 1rem;
}

/* Form styling */
.creamaker-basic-form .form-item {
  margin-bottom: 20px;
}

.creamaker-basic-form .form-submit {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.creamaker-basic-form .form-submit:hover {
  background-color: #0056b3;
}

.creamaker-basic-form .form-required {
  color: #dc3545;
}

/* Focus effects */
.creamaker-basic-form .form-item.focused {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

.creamaker-basic-form .form-item.focused input,
.creamaker-basic-form .form-item.focused textarea {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .creamaker-basic-form {
    padding: 15px;
  }

  .webform-custom-header {
    padding: 20px;
  }

  .webform-custom-header h2 {
    font-size: 1.8rem;
  }
}
