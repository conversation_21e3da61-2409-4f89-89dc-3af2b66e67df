<?php

namespace Drupal\webform_docx_mailer\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\webform\Entity\Webform;
use Symfony\Component\HttpFoundation\Response;

class WebformModalController extends ControllerBase
{

  /**
   * Display webform in modal.
   */
  public function modal()
  {
    $webform = Webform::load('webform_docx_mailer');
    if (!$webform) {
      return new Response('Form not found.', 404);
    }

    // Check access properly for Drupal 10.2
    $access = $webform->access('view', $this->currentUser());
    if (!$access) {
      return new Response('Access denied.', 403);
    }

    // Build the webform using the proper method for Drupal 10.2
    $webform_submission = \Drupal::entityTypeManager()
      ->getStorage('webform_submission')
      ->create(['webform_id' => 'webform_docx_mailer']);

    $form = \Drupal::service('entity.form_builder')->getForm($webform_submission, 'add');

    return [
      '#theme' => 'webform_docx_mailer_modal',
      '#form' => $form,
      '#attached' => [
        'library' => [
          'core/drupal.dialog.ajax',
          'webform_docx_mailer/webform_docx_mailer'
        ],
      ],
      '#cache' => [
        'contexts' => ['user'],
        'tags' => $webform->getCacheTags(),
      ],
    ];
  }
}
