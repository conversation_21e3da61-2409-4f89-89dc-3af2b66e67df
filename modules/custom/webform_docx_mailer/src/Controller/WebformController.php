<?php

namespace Drupal\webform_docx_mailer\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\webform\Entity\Webform;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Controller for displaying the webform at custom URL.
 */
class WebformController extends ControllerBase
{

  /**
   * Displays the webform at /form/creamaker-basic.
   */
  public function showForm(Request $request)
  {
    // Load the webform
    $webform = Webform::load('webform_docx_mailer');

    if (!$webform) {
      throw new NotFoundHttpException('Webform not found.');
    }

    // Check access
    $access = $webform->access('view', $this->currentUser());
    if (!$access) {
      throw new NotFoundHttpException('Access denied.');
    }

    // Create webform submission entity
    $webform_submission = $this->entityTypeManager()
      ->getStorage('webform_submission')
      ->create([
        'webform_id' => $webform->id(),
      ]);

    // Build the form using entity form builder
    $form = \Drupal::service('entity.form_builder')->getForm($webform_submission, 'add');

    // Add custom styling and libraries
    $form['#attached']['library'][] = 'webform_docx_mailer/webform_docx_mailer';

    return [
      '#type' => 'container',
      '#attributes' => ['class' => ['creamaker-basic-form']],
      'form' => $form,
      '#cache' => [
        'contexts' => ['url'],
        'tags' => $webform->getCacheTags(),
        'max-age' => 0,
      ],
    ];
  }
}
