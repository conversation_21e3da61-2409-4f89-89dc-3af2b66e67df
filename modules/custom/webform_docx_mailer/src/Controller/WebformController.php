<?php

namespace Drupal\webform_docx_mailer\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\webform\Entity\Webform;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Controller for displaying the webform at custom URL.
 */
class WebformController extends ControllerBase
{

  /**
   * Displays the webform at /form/creamaker-basic.
   */
  public function showForm()
  {
    // Load the webform
    $webform = Webform::load('webform_docx_mailer');

    if (!$webform) {
      throw new NotFoundHttpException('Webform not found.');
    }

    // Check access
    $access = $webform->access('view', $this->currentUser());
    if (!$access) {
      throw new NotFoundHttpException('Access denied.');
    }

    // Create a simple redirect to the webform's canonical URL
    // This avoids context issues
    $webform_url = $webform->toUrl('canonical');

    // Alternative: Use the view builder approach
    try {
      $view_builder = \Drupal::entityTypeManager()->getViewBuilder('webform');
      $build = $view_builder->view($webform, 'default');

      // Add custom styling and libraries
      $build['#attached']['library'][] = 'webform_docx_mailer/webform_docx_mailer';

      return [
        '#type' => 'container',
        '#attributes' => ['class' => ['creamaker-basic-form']],
        'form' => $build,
        '#cache' => [
          'contexts' => ['user', 'url'],
          'tags' => $webform->getCacheTags(),
          'max-age' => 0,
        ],
      ];
    } catch (\Exception $e) {
      // Fallback: redirect to the standard webform URL
      return $this->redirect('entity.webform.canonical', ['webform' => 'webform_docx_mailer']);
    }
  }
}
