<?php

namespace Drupal\webform_docx_mailer\Controller;

use Drupal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\Request;
use Drupal\webform\Entity\Webform;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ModalFormController extends ControllerBase
{

  /**
   * Display webform in modal form.
   */
  public function modalForm(Request $request)
  {
    $webform = Webform::load('webform_docx_mailer');
    if (!$webform) {
      throw new NotFoundHttpException();
    }

    // Check access properly for Drupal 10.2
    $access = $webform->access('view', $this->currentUser());
    if (!$access) {
      throw new NotFoundHttpException();
    }

    // Build the webform submission form properly for Drupal 10.2
    $webform_submission = \Drupal::entityTypeManager()
      ->getStorage('webform_submission')
      ->create(['webform_id' => 'webform_docx_mailer']);

    $form = \Drupal::service('entity.form_builder')->getForm($webform_submission, 'add');

    return [
      '#type' => 'container',
      '#attributes' => ['class' => ['creafree-full-form']],
      'form' => $form,
      '#attached' => [
        'library' => ['webform_docx_mailer/webform_docx_mailer'],
      ],
      '#cache' => [
        'contexts' => ['user'],
        'tags' => $webform->getCacheTags(),
      ],
    ];
  }
}
