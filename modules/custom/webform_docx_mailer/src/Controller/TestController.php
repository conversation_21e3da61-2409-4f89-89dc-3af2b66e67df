<?php

namespace Drupal\webform_docx_mailer\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\Url;

/**
 * Controller for testing the webform modal functionality.
 */
class TestController extends ControllerBase
{

  /**
   * Test page to demonstrate webform functionality.
   */
  public function testPage()
  {
    $form_url = Url::fromRoute('webform_docx_mailer.modal_form');

    return [
      '#type' => 'container',
      '#attributes' => ['class' => ['webform-test-page']],
      'title' => [
        '#type' => 'html_tag',
        '#tag' => 'h1',
        '#value' => $this->t('Webform DOCX Mailer Test Page'),
      ],
      'description' => [
        '#type' => 'html_tag',
        '#tag' => 'p',
        '#value' => $this->t('This page allows you to test the different ways to display the webform.'),
      ],
      'form_link' => [
        '#type' => 'link',
        '#title' => $this->t('View Form Page'),
        '#url' => $form_url,
        '#attributes' => [
          'class' => ['btn', 'btn-primary'],
        ],
      ],
      'standard_form_link' => [
        '#type' => 'link',
        '#title' => $this->t('Standard Form Page'),
        '#url' => Url::fromRoute('entity.webform.canonical', ['webform' => 'webform_docx_mailer']),
        '#attributes' => [
          'class' => ['btn', 'btn-info'],
        ],
      ],
      '#attached' => [
        'library' => [
          'webform_docx_mailer/webform_docx_mailer',
        ],
      ],
    ];
  }
}
