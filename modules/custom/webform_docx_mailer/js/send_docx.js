(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.webformDocxMailer = {
    attach: function (context, settings) {
      // Use once() for Drupal 10.2 compatibility
      $(once('webform-docx-mailer', '#webform-submission-webform-docx-mailer-add-form', context)).on('submit', function (e) {
        const email = $('input[name="email_address"]').val();

        if (email) {
          $.ajax({
            url: '/send-docx-email',
            type: 'POST',
            data: { email: email },
            success: function (response) {
              console.log('Email sent:', response);
            },
            error: function (xhr) {
              console.error('Error sending email:', xhr.responseText);
            }
          });
        }
      });

      // Handle modal forms
      $(once('webform-modal-docx-mailer', '.webform-docx-mailer-modal form', context)).on('submit', function (e) {
        const $form = $(this);
        const email = $form.find('input[name="email_address"]').val();

        if (email) {
          // Let the form submit normally, the hook will handle the email
          console.log('Form submitted with email:', email);
        }
      });
    }
  };
})(jQuery, Drupal, once);
