(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.webformDocxMailer = {
    attach: function (context, settings) {
      // Use once() for Drupal 10.2 compatibility
      $(once('webform-docx-mailer', '#webform-submission-webform-docx-mailer-add-form', context)).on('submit', function (e) {
        const email = $('input[name="email_address"]').val();
        const firstName = $('input[name="first_name_s"]').val();
        const lastName = $('input[name="last_name"]').val();

        if (email) {
          // Show a loading message
          const $submitButton = $(this).find('.form-submit');
          const originalText = $submitButton.val();
          $submitButton.val('Sending...').prop('disabled', true);

          // Log the submission
          console.log('Form submitted with:', {
            email: email,
            firstName: firstName,
            lastName: lastName
          });

          // Re-enable button after a short delay (the form will redirect anyway)
          setTimeout(function() {
            $submitButton.val(originalText).prop('disabled', false);
          }, 3000);
        }
      });


      // Add some visual feedback
      $(once('webform-field-focus', '.creamaker-basic-form input, .creamaker-basic-form textarea', context)).on('focus', function() {
        $(this).closest('.form-item').addClass('focused');
      }).on('blur', function() {
        $(this).closest('.form-item').removeClass('focused');
      });
    }
  };
})(jQuery, Drupal, once);
