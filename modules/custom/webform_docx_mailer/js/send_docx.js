(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.webformDocxMailer = {
    attach: function (context, settings) {
      // Use once() for Drupal 10.2 compatibility
      $(once('webform-docx-mailer', '#webform-submission-webform-docx-mailer-add-form', context)).on('submit', function (e) {
        const email = $('input[name="email_address"]').val();

        if (email) {
          $.ajax({
            url: '/send-docx-email',
            type: 'POST',
            data: { email: email },
            success: function (response) {
              console.log('Email sent:', response);
            },
            error: function (xhr) {
              console.error('Error sending email:', xhr.responseText);
            }
          });
        }
      });


    }
  };
})(jQ<PERSON><PERSON>, <PERSON>upal, once);
