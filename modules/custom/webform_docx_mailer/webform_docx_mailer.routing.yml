send_docx_email:
  path: '/send-docx-email'
  defaults:
    _controller: '\Drupal\webform_docx_mailer\Controller\SendDocxController::send'
  requirements:
    _permission: 'access content'
  methods: [POST]

webform_docx_mailer.modal_form:
  path: '/webform-docx-modal'
  defaults:
    _controller: '\Drupal\webform_docx_mailer\Controller\ModalFormController::modalForm'
    _title: 'CreaMAKER Basic'
  requirements:
    _permission: 'access content'

webform_docx_mailer.test:
  path: '/webform-docx-test'
  defaults:
    _controller: '\Drupal\webform_docx_mailer\Controller\TestController::testPage'
    _title: 'Webform DOCX Mailer Test'
  requirements:
    _permission: 'access content'
