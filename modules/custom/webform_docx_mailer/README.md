# Webform DOCX Mailer

Ce module Drupal 10.2+ envoie automatiquement un email avec un fichier DOCX en pièce jointe lorsqu'un utilisateur soumet le formulaire webform spécifique.

## Fonctionnalités

- Envoi automatique d'email après soumission du webform `webform_docx_mailer`
- Pièce jointe automatique du fichier `creamakerbasic.docx`
- Compatible avec Symfony Mailer (Drupal 10.2+)
- Affichage modal du formulaire avec contenu complet
- Validation des champs requis
- Logging des erreurs et succès
- Templates personnalisés pour l'affichage modal

## Installation

1. Assurez-vous que le module Webform est installé et activé
2. Placez ce module dans `modules/custom/webform_docx_mailer/`
3. Assurez-vous que le fichier `creamakerbasic.docx` est présent dans `sites/default/files/`
4. Activez le module via l'interface d'administration : `/admin/modules`

## Configuration

### Fichier DOCX

Le fichier `creamakerbasic.docx` doit être présent dans le répertoire `sites/default/files/`. Le module le cherchera automatiquement à cet emplacement.

### Webform

Le module fonctionne avec un webform ayant l'ID `webform_docx_mailer` et les champs suivants :

- `first_name_s` : Prénom(s) de l'utilisateur
- `last_name` : Nom de famille
- `email_address` : Adresse email (requis)
- `i_wish_to_receive_the_creafree_newsletter` : Case à cocher pour la newsletter

## Utilisation

1. L'utilisateur accède au formulaire : `/form/creamaker-basic`
2. Il remplit les champs requis
3. Après soumission, un email est automatiquement envoyé à l'adresse fournie
4. L'email contient le fichier `creamakerbasic.docx` en pièce jointe

## Structure de l'email

- **Sujet** : "Your CreaMAKER Basic Document"
- **Corps** : Message personnalisé avec le nom de l'utilisateur
- **Pièce jointe** : `creamakerbasic.docx`

## Logs et débogage

Le module enregistre ses activités dans les logs Drupal :

- Succès d'envoi d'email
- Erreurs de validation
- Fichiers manquants
- Erreurs d'envoi

Consultez les logs à : `/admin/reports/dblog`

## Corrections pour Drupal 10.2

### Problèmes résolus

1. **Contenu du webform non visible dans la popup** :

   - Ajout du template `webform-docx-mailer-modal.html.twig`
   - Correction des contrôleurs pour utiliser `entity.form_builder`
   - Mise à jour du JavaScript avec la syntaxe `once()` pour Drupal 10.2

2. **Compatibilité Drupal 10.2** :

   - Mise à jour des dépendances JavaScript (ajout de `core/once`)
   - Correction des méthodes d'accès aux entités
   - Amélioration de la gestion du cache

3. **Améliorations de l'interface** :
   - Ajout de styles CSS pour la modal
   - Template personnalisé pour l'affichage du formulaire
   - Meilleure intégration avec Bootstrap 5

### Routes disponibles

- `/webform-docx-modal` : Affichage du formulaire en mode modal simple
- `/webform-docx-modal-popup` : Affichage du formulaire en popup modal
- `/form/creamaker-basic` : Page standard du formulaire

## Dépendances

- Drupal 10.2+
- Module Webform
- Symfony Mailer (inclus dans Drupal 10)

## Développement

### Hooks utilisés

- `hook_webform_submission_insert()` : Détecte les nouvelles soumissions
- `hook_mail()` : Configure l'email et les pièces jointes

### Fonctions principales

- `webform_docx_mailer_send_email()` : Gère l'envoi de l'email
- `webform_docx_mailer_mail()` : Configure le message et les attachments

## Dépannage

### L'email n'est pas envoyé

1. Vérifiez que le module est activé
2. Vérifiez la configuration email de Drupal
3. Consultez les logs pour les erreurs

### La pièce jointe n'apparaît pas

1. Vérifiez que le fichier `creamakerbasic.docx` existe dans `sites/default/files/`
2. Vérifiez les permissions du fichier
3. Consultez les logs pour les erreurs de fichier

### Erreurs de validation

Le module valide que l'adresse email est fournie. Assurez-vous que le champ `email_address` est correctement rempli.

## Support

Pour les problèmes ou questions, consultez les logs Drupal et vérifiez la configuration du système de mail.
