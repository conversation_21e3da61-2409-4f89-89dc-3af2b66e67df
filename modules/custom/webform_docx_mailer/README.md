# Webform DOCX Mailer

Ce module Drupal 10.2+ envoie automatiquement un email avec un fichier DOCX en pièce jointe lorsqu'un utilisateur soumet le formulaire webform spécifique.

## Fonctionnalités

- Envoi automatique d'email après soumission du webform `webform_docx_mailer`
- Pièce jointe automatique du fichier `creamakerbasic.docx`
- Compatible avec Symfony Mailer (Drupal 10.2+)
- Affichage du formulaire à l'URL personnalisée `/form/creamaker-basic`
- Interface utilisateur moderne et responsive
- Validation des champs requis
- Logging des erreurs et succès
- Effets visuels et feedback utilisateur

## Installation

1. Assurez-vous que le module Webform est installé et activé
2. Placez ce module dans `modules/custom/webform_docx_mailer/`
3. Assurez-vous que le fichier `creamakerbasic.docx` est présent dans `sites/default/files/`
4. Activez le module via l'interface d'administration : `/admin/modules`

## Configuration

### Fichier DOCX

Le fichier `creamakerbasic.docx` doit être présent dans le répertoire `sites/default/files/`. Le module le cherchera automatiquement à cet emplacement.

### Webform

Le module fonctionne avec un webform ayant l'ID `webform_docx_mailer` et les champs suivants :

- `first_name_s` : Prénom(s) de l'utilisateur
- `last_name` : Nom de famille
- `email_address` : Adresse email (requis)
- `i_wish_to_receive_the_creafree_newsletter` : Case à cocher pour la newsletter

## Utilisation

1. L'utilisateur accède au formulaire : `/form/creamaker-basic`
2. Le formulaire s'affiche avec un en-tête personnalisé et des instructions claires
3. L'utilisateur remplit les champs requis avec des effets visuels de feedback
4. Après soumission, un email est automatiquement envoyé à l'adresse fournie
5. L'email contient le fichier `creamakerbasic.docx` en pièce jointe

## Structure de l'email

- **Sujet** : "Your CreaMAKER Basic Document"
- **Corps** : Message personnalisé avec le nom de l'utilisateur
- **Pièce jointe** : `creamakerbasic.docx`

## Logs et débogage

Le module enregistre ses activités dans les logs Drupal :

- Succès d'envoi d'email
- Erreurs de validation
- Fichiers manquants
- Erreurs d'envoi

Consultez les logs à : `/admin/reports/dblog`

## Version refaite pour Drupal 10.2

### Nouvelles fonctionnalités

1. **URL personnalisée simple** :

   - Le formulaire est maintenant accessible directement à `/form/creamaker-basic`
   - Plus de complexité avec des modals ou des popups
   - Affichage direct et simple du formulaire

2. **Interface utilisateur améliorée** :

   - En-tête personnalisé avec instructions claires
   - Design responsive et moderne
   - Effets visuels lors de la saisie (focus, hover)
   - Feedback utilisateur lors de la soumission

3. **Code simplifié** :
   - Un seul contrôleur `WebformController`
   - Une seule route `/form/creamaker-basic`
   - CSS et JavaScript optimisés
   - Compatible Drupal 10.2+ avec `once()`

### Route disponible

- `/form/creamaker-basic` : Formulaire BASIC CreaMAKER avec interface moderne

## Dépendances

- Drupal 10.2+
- Module Webform
- Symfony Mailer (inclus dans Drupal 10)

## Développement

### Hooks utilisés

- `hook_webform_submission_insert()` : Détecte les nouvelles soumissions
- `hook_mail()` : Configure l'email et les pièces jointes

### Fonctions principales

- `webform_docx_mailer_send_email()` : Gère l'envoi de l'email
- `webform_docx_mailer_mail()` : Configure le message et les attachments

## Dépannage

### L'email n'est pas envoyé

1. Vérifiez que le module est activé
2. Vérifiez la configuration email de Drupal
3. Consultez les logs pour les erreurs

### La pièce jointe n'apparaît pas

1. Vérifiez que le fichier `creamakerbasic.docx` existe dans `sites/default/files/`
2. Vérifiez les permissions du fichier
3. Consultez les logs pour les erreurs de fichier

### Erreurs de validation

Le module valide que l'adresse email est fournie. Assurez-vous que le champ `email_address` est correctement rempli.

### **Pour tester :**

1. Activez le module : `drush en webform_docx_mailer -y`
2. Visitez `/form/creamaker-basic` pour voir le formulaire
3. Assurez-vous que le fichier `creamakerbasic.docx` est dans `sites/default/files/`
4. Testez la soumission du formulaire pour vérifier l'envoi d'email

Le module est maintenant entièrement refait, simplifié et compatible avec Drupal 10.2 !

## Support

Pour les problèmes ou questions, consultez les logs Drupal et vérifiez la configuration du système de mail.
