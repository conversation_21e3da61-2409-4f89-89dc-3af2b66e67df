<?php

/**
 * @file
 * Install, update and uninstall functions for the webform_docx_mailer module.
 */

use Drupal\webform\Entity\Webform;

/**
 * Implements hook_install().
 */
function webform_docx_mailer_install() {
  // Check if the webform already exists
  $webform = Webform::load('webform_docx_mailer');
  
  if (!$webform) {
    \Drupal::messenger()->addMessage(t('The webform_docx_mailer webform has been created. You can access it at /form/creamaker-basic'));
  }
  
  // Check if the DOCX file exists
  $file_path = 'public://creamakerbasic.docx';
  $file_system = \Drupal::service('file_system');
  $real_path = $file_system->realpath($file_path);
  
  if (!$real_path || !file_exists($real_path)) {
    \Drupal::messenger()->addWarning(t('Please make sure to upload the creamakerbasic.docx file to the sites/default/files/ directory for the module to work properly.'));
  }
  
  \Drupal::messenger()->addMessage(t('Webform DOCX Mailer module has been installed successfully.'));
}

/**
 * Implements hook_uninstall().
 */
function webform_docx_mailer_uninstall() {
  // Optionally remove the webform when uninstalling
  $webform = Webform::load('webform_docx_mailer');
  if ($webform) {
    $webform->delete();
    \Drupal::messenger()->addMessage(t('The webform_docx_mailer webform has been removed.'));
  }
}

/**
 * Implements hook_requirements().
 */
function webform_docx_mailer_requirements($phase) {
  $requirements = [];
  
  if ($phase == 'runtime') {
    // Check if webform module is enabled
    if (!\Drupal::moduleHandler()->moduleExists('webform')) {
      $requirements['webform_docx_mailer_webform'] = [
        'title' => t('Webform DOCX Mailer'),
        'value' => t('Webform module required'),
        'description' => t('The Webform module is required for Webform DOCX Mailer to function.'),
        'severity' => REQUIREMENT_ERROR,
      ];
    }
    
    // Check if the webform exists
    $webform = Webform::load('webform_docx_mailer');
    if (!$webform) {
      $requirements['webform_docx_mailer_form'] = [
        'title' => t('Webform DOCX Mailer'),
        'value' => t('Webform missing'),
        'description' => t('The webform_docx_mailer webform is missing. Please reinstall the module.'),
        'severity' => REQUIREMENT_WARNING,
      ];
    }
    
    // Check if the DOCX file exists
    $file_path = 'public://creamakerbasic.docx';
    $file_system = \Drupal::service('file_system');
    $real_path = $file_system->realpath($file_path);
    
    if (!$real_path || !file_exists($real_path)) {
      $requirements['webform_docx_mailer_file'] = [
        'title' => t('Webform DOCX Mailer'),
        'value' => t('DOCX file missing'),
        'description' => t('The creamakerbasic.docx file is missing from sites/default/files/. Please upload the file for the module to work properly.'),
        'severity' => REQUIREMENT_WARNING,
      ];
    } else {
      $requirements['webform_docx_mailer_file'] = [
        'title' => t('Webform DOCX Mailer'),
        'value' => t('DOCX file found'),
        'description' => t('The creamakerbasic.docx file is properly located.'),
        'severity' => REQUIREMENT_OK,
      ];
    }
  }
  
  return $requirements;
}
