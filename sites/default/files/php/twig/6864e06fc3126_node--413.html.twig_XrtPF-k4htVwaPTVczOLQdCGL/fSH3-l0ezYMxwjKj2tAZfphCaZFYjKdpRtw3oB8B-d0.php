<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* themes/contrib/bootstrap5/templates/content/node--413.html.twig */
class __TwigTemplate_bf8630c603e00a7ec9b9d99efcf965bc extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 70
        echo "<script type=\"application/ld+json\">
\t{
\t  \"@context\": \"https://schema.org\",
\t  \"@type\": \"WebPage\",
\t  \"name\": \"CreaFREE Ecosystem\",
\t  \"url\": \"https://creafree.org/aboutus\",
\t  \"description\": \"CreaFREE Ecosystem implements the Universal Standard for Intellectual Property (USIP), managed by the World creators Society.\",
\t  \"publisher\": {
\t    \"@type\": \"Organization\",
\t    \"name\": \"CreaFREE\",
\t    \"url\": \"https://creafree.org/\",
\t    \"logo\": {
\t      \"@type\": \"ImageObject\",
\t      \"url\": \"https://creafree.org/logov2.png\"
\t    }
\t  },
\t  \"mainContentOfPage\": [
\t    {
\t      \"@type\": \"WebPageElement\",
\t      \"isPartOf\": {
\t        \"@id\": \"#CreaFREEEcosystem\"
\t      },
\t      \"name\": \"CreaFREE Ecosystem\",
\t      \"description\": \"Overview of the CreaFREE Ecosystem structures and actors\"
\t    },
\t    {
\t      \"@type\": \"WebPageElement\",
\t      \"isPartOf\": {
\t        \"@id\": \"#wcs\"
\t      },
\t      \"name\": \"World creators Society\",
\t      \"description\": \"Information about the World creators Society\"
\t    }
\t  ],
\t  \"mainEntity\": {
\t    \"@type\": \"ItemList\",
\t    \"itemListElement\": [
\t      {
\t        \"@type\": \"ListItem\",
\t        \"position\": 1,
\t        \"item\": {
\t          \"@type\": \"Thing\",
\t          \"name\": \"Structures\",
\t          \"description\": \"USIP: Universal standard for intellectual property defines the general structure of the ecosystem\"
\t        }
\t      },
\t      {
\t        \"@type\": \"ListItem\",
\t        \"position\": 2,
\t        \"item\": {
\t          \"@type\": \"Thing\",
\t          \"name\": \"CreaPOLE\",
\t          \"description\": \"Organisation mandated by the WcS to manage a global sector of innovation\"
\t        }
\t      },
\t      {
\t        \"@type\": \"ListItem\",
\t        \"position\": 3,
\t        \"item\": {
\t          \"@type\": \"Thing\",
\t          \"name\": \"Mutual creations Insurance\",
\t          \"description\": \"Structure organized to insure the value of the certificated issued by WcS\"
\t        }
\t      },
\t      {
\t        \"@type\": \"ListItem\",
\t        \"position\": 4,
\t        \"item\": {
\t          \"@type\": \"Thing\",
\t          \"name\": \"High court of arbitration\",
\t          \"description\": \"Jury established at a global level to settle a case relating to the infringement of a CreaBOOK\"
\t        }
\t      },
\t      {
\t        \"@type\": \"ListItem\",
\t        \"position\": 5,
\t        \"item\": {
\t          \"@type\": \"Organization\",
\t          \"name\": \"World creators Society\",
\t          \"description\": \"Founded to implement Article 27 of the Universal Declaration of Human Rights\"
\t        }
\t      }
\t    ]
\t  }
\t}
</script>

";
        // line 158
        $context["classes"] = ["node", ("node--type-" . \Drupal\Component\Utility\Html::getClass($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source,         // line 160
($context["node"] ?? null), "bundle", [], "any", false, false, true, 160), 160, $this->source))), ((twig_get_attribute($this->env, $this->source,         // line 161
($context["node"] ?? null), "isPromoted", [], "method", false, false, true, 161)) ? ("node--promoted") : ("")), ((twig_get_attribute($this->env, $this->source,         // line 162
($context["node"] ?? null), "isSticky", [], "method", false, false, true, 162)) ? ("node--sticky") : ("")), (( !twig_get_attribute($this->env, $this->source,         // line 163
($context["node"] ?? null), "isPublished", [], "method", false, false, true, 163)) ? ("node--unpublished") : ("")), ((        // line 164
($context["view_mode"] ?? null)) ? (("node--view-mode-" . \Drupal\Component\Utility\Html::getClass($this->sandbox->ensureToStringAllowed(($context["view_mode"] ?? null), 164, $this->source)))) : (""))];
        // line 167
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->attachLibrary("bootstrap5/node"), "html", null, true);
        echo "
<article";
        // line 168
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", [($context["classes"] ?? null)], "method", false, false, true, 168), 168, $this->source), "html", null, true);
        echo ">

\t";
        // line 170
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_prefix"] ?? null), 170, $this->source), "html", null, true);
        echo "
\t";
        // line 171
        if ((($context["label"] ?? null) &&  !($context["page"] ?? null))) {
            // line 172
            echo "\t\t<h2";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_attributes"] ?? null), 172, $this->source), "html", null, true);
            echo ">
\t\t\t<a href=\"";
            // line 173
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["url"] ?? null), 173, $this->source), "html", null, true);
            echo "\" rel=\"bookmark\">";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["label"] ?? null), 173, $this->source), "html", null, true);
            echo "</a>
\t\t</h2>
\t";
        }
        // line 176
        echo "\t";
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_suffix"] ?? null), 176, $this->source), "html", null, true);
        echo "

\t";
        // line 178
        if (($context["display_submitted"] ?? null)) {
            // line 179
            echo "\t\t<footer class=\"node__meta\">
\t\t\t";
            // line 180
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["author_picture"] ?? null), 180, $this->source), "html", null, true);
            echo "
\t\t\t<div";
            // line 181
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["author_attributes"] ?? null), "addClass", ["node__submitted"], "method", false, false, true, 181), 181, $this->source), "html", null, true);
            echo ">
\t\t\t\t";
            // line 182
            echo t("Submitted by
\t\t\t\t@author_name
\t\t\t\ton
\t\t\t\t@date", array("@author_name" =>             // line 183
($context["author_name"] ?? null), "@date" =>             // line 185
($context["date"] ?? null), ));
            // line 186
            echo "\t\t\t\t";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["metadata"] ?? null), 186, $this->source), "html", null, true);
            echo "
\t\t\t</div>
\t\t</footer>
\t";
        }
        // line 190
        echo "
\t<div";
        // line 191
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["content_attributes"] ?? null), "addClass", ["node__content"], "method", false, false, true, 191), 191, $this->source), "html", null, true);
        echo ">
\t\t";
        // line 192
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["content"] ?? null), 192, $this->source), "html", null, true);
        echo "
\t\t<!-- creacorn -->

\t\t<div class=\"container-fluid m-0 text-dark creafree-layout\">
\t\t\t<div
\t\t\t\tclass=\"row\">
\t\t\t\t<!-- 1/3 Left Section for CreaMAKER Download and Description -->
\t\t\t\t<div class=\"col-md-4 col-sm-12 pt-5 fondbleu2 sticky-left\">
\t\t\t\t\t<div class=\"container text-dark text-start\">
\t\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t\t<h1 class=\"fs-1 text-start text-light fw-bold\">
\t\t\t\t\t\t\t\t<svg class=\"bi bi-square-fill\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" fill=\"#f29432\" viewbox=\"0 0 16 16\">
\t\t\t\t\t\t\t\t\t<path d=\"M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"></path>
\t\t\t\t\t\t\t\t</svg>
\t\t\t\t\t\t\t\tBASIC CreaMAKER
\t\t\t\t\t\t\t</h1>
\t\t\t\t\t\t\t<p class=\"text-light fs-4 text-start mx-3 p-4 pt-2\">
\t\t\t\t\t\t\t\tPrivate patent protecting<br>
your innovation at zero cost<br>
Learn how to make a Basic CreaBOOK
\t\t\t\t\t\t\t</p>
\t\t\t\t\t\t\t<ul class=\"list-unstyled text-light fs-4 text-start p-4 pt-2\"><li class=\"text-light fs-6 text-start mx-3\">Free step by step guide to draft
your private patent<li>
<li class=\"text-light fs-6 text-start mx-3\">Ensure your return on investment
through a long term and world wide protection<li>
<li class=\"text-light fs-6 text-start mx-3 \">Prove the primacy of your authorship
through timestamping<li>

</ul>

<p class=\"text-center\">
<img loading=\"lazy\" src=\"https://creafree.org/imagecreamakerbasic.png\" width=\"291\" height=\"336\" alt=\"creamakerbasic\"></p>

<a href=\"/form/creamaker-basic\" class=\"btn btn-primary btn-sm\" role=\"button\" aria-pressed=\"true\">Download Now</a>

<a href=\"/webform-docx-modal\" class=\"use-ajax btn btn-primary\" data-dialog-type=\"modal\" data-dialog-options='{\"width\":800}'>
  Get My BASIC CreaMAKER Form
</a>


\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t<!-- 2/3 Right Section with Lists and Text Block -->
\t\t\t\t<div class=\"col-md-8 col-sm-12 pt-2 scrollable-right\">
    <div class=\"px-4 py-3\">
        <div class=\"mb-4\">
            <div class=\"clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item\">
                <div class=\"container py-2\">
                    <div class=\"mb-5\">
                        <h1 class=\"text-start fw-bold display-6 custom-title\">
                            Basic CreaMAKER <br>The essential tool to protect<br>
and showcase your innovation - <br>without traditional patents
                        </h1>
                       
                    </div>
                    <section class=\"mb-5\">
                         <h2 class=\"h4 fw-bold\">
                            Content and purpose
                        </h2>
                        <div class=\"service-item mb-4\">
                           <p>BASIC CreaMAKER is a simple and structured private patent compatible with the CreaFREE Standard. It allows any creator to formalize his idea, prove its originality and timetamp its primacy</p>
                        </div>
                        <div class=\"service-item mb-4\">
                            <p>
                                This private patent is made for entrepreneurs, startup founders, students, searchers, inventors, artists, engineers, companies, NGOs, local authorities — anyone who innovates and needs IP without the costs, delays and complexities of the traditional state patents.
                            </p>
                        </div>
                        <div class=\"service-item mb-4\">
                            <p>
                                In order to finance the necessary research and development required to bring your innovation to market, your startup must obtain the support of venture capitalists. This support depends on the return on the investment (ROI) you can offer. To guarantee ROI, your startup needs efficient intellectual property protecting its innovation.
                            </p>
                        </div>
                         <div class=\"service-item mb-4\">
                            <p>
                                With this new tool, your startup can register its private patent and immediately disclose its protected content to share its attractive return with partners and investors.
                            </p>
                        </div>
                    </section>
                </div>
                  <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        Why you will get?
                    </h2>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Tutorial:</u></strong> Guide to design your CreaBOOK.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Free download:</u></strong> Access to the tutorial at zero cost.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Step-by-step guide:</u></strong> Describe and prove the authenticity of your claims.
                        </p>
                    </div>
                     <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Primacy proof:</u></strong> Timestamp your CreaBOOK with the provided tool and establish the primacy of your author rights.
                        </p>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                       Why is CreaBOOK Protection Unique?
                    </h2>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Competitive Advantage:</u></strong> The private patent protects all your creative ideas against unfair competitors.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Global market:</u></strong> Anchors your startup property worldwide.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Attract Investors:</u></strong> Long term protection which lasts during all your life plus at least 50 years after your death.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Free, fast and easy to use:</u></strong> Your first CreaBOOK can be written, registered and published within a few hours.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Flexible:</u></strong> Can be upgraded with the assistance of accredited IP coaches.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Certified:</u></strong> Once registered and upgraded as GOLD CreaBOOK, the conformity, value and originality of your CreaBOOK can be certified by World creators Society.
                        </p>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        How to Proceed?
                    </h2>
                    <div class=\"steps-container py-3\">
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>1.</strong> <u>Download the Tutorial:</u> Click on the orange button at the end of the blue column.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>2.</strong> <u>Make a copy of the tutorial file:</u> Work offline in complete confidentiality before publishing.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>3.</strong> <u>Refer to documentary evidence:</u> List all your primacy evidence in annexes (sketches, articles, mails, photos, crypted know-how …) and timestamp each of them.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>4.</strong> <u>Establish the priority of your primacy:</u> Timestamp your private patent before registration on the World creations Registry.
                            </p>
                        </div>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        Next Step: Publish Your CreaBOOK
                    </h2>
                    <p>
                        To assert your claims and benefit from the other services of the CreaFREE ecosystem, publish your CreaBOOK in the World creations Register.
                    </p>
                    
                </section>
            </div>
        </div>
    </div>
</div>
\t\t</div>
\t</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["node", "view_mode", "attributes", "title_prefix", "label", "page", "title_attributes", "url", "title_suffix", "display_submitted", "author_picture", "author_attributes", "author_name", "date", "metadata", "content_attributes", "content"]);    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "themes/contrib/bootstrap5/templates/content/node--413.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  204 => 192,  200 => 191,  197 => 190,  189 => 186,  187 => 185,  186 => 183,  182 => 182,  178 => 181,  174 => 180,  171 => 179,  169 => 178,  163 => 176,  155 => 173,  150 => 172,  148 => 171,  144 => 170,  139 => 168,  135 => 167,  133 => 164,  132 => 163,  131 => 162,  130 => 161,  129 => 160,  128 => 158,  39 => 70,);
    }

    public function getSourceContext()
    {
        return new Source("", "themes/contrib/bootstrap5/templates/content/node--413.html.twig", "/var/www/html/web/themes/contrib/bootstrap5/templates/content/node--413.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array("set" => 158, "if" => 171, "trans" => 182);
        static $filters = array("clean_class" => 160, "escape" => 167);
        static $functions = array("attach_library" => 167);

        try {
            $this->sandbox->checkSecurity(
                ['set', 'if', 'trans'],
                ['clean_class', 'escape'],
                ['attach_library']
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
