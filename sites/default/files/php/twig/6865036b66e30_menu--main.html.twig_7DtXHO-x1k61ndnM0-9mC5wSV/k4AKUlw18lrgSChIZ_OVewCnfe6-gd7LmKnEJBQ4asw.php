<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* themes/contrib/bootstrap5/templates/navigation/menu--main.html.twig */
class __TwigTemplate_5491c6aefe0eb0dd362b58fac3e37bd9 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 7
        $macros["menus"] = $this->macros["menus"] = $this;
        // line 8
        echo "
";
        // line 9
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(twig_call_macro($macros["menus"], "macro_build_menu", [($context["items"] ?? null), ($context["attributes"] ?? null), 0], 9, $context, $this->getSourceContext()));
        echo "

";
        // line 28
        echo "
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["_self", "items", "attributes", "menu_level"]);    }

    // line 11
    public function macro_build_menu($__items__ = null, $__attributes__ = null, $__menu_level__ = null, ...$__varargs__)
    {
        $macros = $this->macros;
        $context = $this->env->mergeGlobals([
            "items" => $__items__,
            "attributes" => $__attributes__,
            "menu_level" => $__menu_level__,
            "varargs" => $__varargs__,
        ]);

        $blocks = [];

        ob_start(function () { return ''; });
        try {
            // line 12
            echo "  ";
            $macros["menus"] = $this;
            // line 13
            echo "  ";
            if (($context["items"] ?? null)) {
                // line 14
                echo "    ";
                // line 15
                $context["ul_classes"] = [(((                // line 16
($context["menu_level"] ?? null) == 0)) ? ("navmenu") : ("")), (((                // line 17
($context["menu_level"] ?? null) == 0)) ? ("navbar-nav") : ("dropdown-menu")), ("nav-level-" . $this->sandbox->ensureToStringAllowed(                // line 18
($context["menu_level"] ?? null), 18, $this->source))];
                // line 21
                echo "    <ul";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", [($context["ul_classes"] ?? null)], "method", false, false, true, 21), 21, $this->source), "html", null, true);
                echo ">
      ";
                // line 22
                $context['_parent'] = $context;
                $context['_seq'] = twig_ensure_traversable(($context["items"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
                    // line 23
                    echo "        ";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(twig_call_macro($macros["menus"], "macro_add_link", [$context["item"], twig_get_attribute($this->env, $this->source, ($context["attributes"] ?? null), "removeClass", [($context["ul_classes"] ?? null)], "method", false, false, true, 23), ($context["menu_level"] ?? null)], 23, $context, $this->getSourceContext()));
                    echo "
      ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_iterated'], $context['_key'], $context['item'], $context['_parent'], $context['loop']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 25
                echo "    </ul>
  ";
            }

            return ('' === $tmp = ob_get_contents()) ? '' : new Markup($tmp, $this->env->getCharset());
        } finally {
            ob_end_clean();
        }
    }

    // line 29
    public function macro_add_link($__item__ = null, $__attributes__ = null, $__menu_level__ = null, ...$__varargs__)
    {
        $macros = $this->macros;
        $context = $this->env->mergeGlobals([
            "item" => $__item__,
            "attributes" => $__attributes__,
            "menu_level" => $__menu_level__,
            "varargs" => $__varargs__,
        ]);

        $blocks = [];

        ob_start(function () { return ''; });
        try {
            // line 30
            echo "  ";
            $macros["menus"] = $this;
            // line 31
            echo "  ";
            // line 32
            $context["list_item_classes"] = ["nav-item", ((twig_get_attribute($this->env, $this->source,             // line 34
($context["item"] ?? null), "is_expanded", [], "any", false, false, true, 34)) ? ("dropdown") : ("")), ((twig_get_attribute($this->env, $this->source,             // line 35
($context["item"] ?? null), "in_active_trail", [], "any", false, false, true, 35)) ? ("active") : (""))];
            // line 38
            echo "  ";
            // line 39
            $context["link_classes"] = [(((            // line 40
($context["menu_level"] ?? null) == 0)) ? ("nav-link") : ("dropdown-item")), (((twig_get_attribute($this->env, $this->source,             // line 41
($context["item"] ?? null), "is_expanded", [], "any", false, false, true, 41) && (($context["menu_level"] ?? null) == 0))) ? ("dropdown-toggle") : ("")), ((twig_get_attribute($this->env, $this->source,             // line 42
($context["item"] ?? null), "in_active_trail", [], "any", false, false, true, 42)) ? ("active") : (""))];
            // line 45
            echo "  <li";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "attributes", [], "any", false, false, true, 45), "addClass", [($context["list_item_classes"] ?? null)], "method", false, false, true, 45), 45, $this->source), "html", null, true);
            echo ">
    ";
            // line 46
            if (twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "below", [], "any", false, false, true, 46)) {
                // line 47
                echo "      ";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->getLink($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "title", [], "any", false, false, true, 47), 47, $this->source), $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "url", [], "any", false, false, true, 47), 47, $this->source), ["class" =>                 // line 48
($context["link_classes"] ?? null), "data-bs-toggle" => (((                // line 49
($context["menu_level"] ?? null) == 0)) ? ("dropdown") : (null)), "aria-expanded" => "false", "title" => ("Expand menu " . $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source,                 // line 51
($context["item"] ?? null), "title", [], "any", false, false, true, 51), 51, $this->source)), "role" => "button"]), "html", null, true);
                // line 53
                echo "
      ";
                // line 54
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(twig_call_macro($macros["menus"], "macro_build_menu", [twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "below", [], "any", false, false, true, 54), ($context["attributes"] ?? null), (($context["menu_level"] ?? null) + 1)], 54, $context, $this->getSourceContext()));
                echo "
    ";
            } else {
                // line 56
                echo "      ";
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->getLink($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "title", [], "any", false, false, true, 56), 56, $this->source), $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["item"] ?? null), "url", [], "any", false, false, true, 56), 56, $this->source), ["class" => ($context["link_classes"] ?? null)]), "html", null, true);
                echo "
    ";
            }
            // line 58
            echo "  </li>
";

            return ('' === $tmp = ob_get_contents()) ? '' : new Markup($tmp, $this->env->getCharset());
        } finally {
            ob_end_clean();
        }
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "themes/contrib/bootstrap5/templates/navigation/menu--main.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  170 => 58,  164 => 56,  159 => 54,  156 => 53,  154 => 51,  153 => 49,  152 => 48,  150 => 47,  148 => 46,  143 => 45,  141 => 42,  140 => 41,  139 => 40,  138 => 39,  136 => 38,  134 => 35,  133 => 34,  132 => 32,  130 => 31,  127 => 30,  112 => 29,  101 => 25,  92 => 23,  88 => 22,  83 => 21,  81 => 18,  80 => 17,  79 => 16,  78 => 15,  76 => 14,  73 => 13,  70 => 12,  55 => 11,  49 => 28,  44 => 9,  41 => 8,  39 => 7,);
    }

    public function getSourceContext()
    {
        return new Source("", "themes/contrib/bootstrap5/templates/navigation/menu--main.html.twig", "/var/www/html/web/themes/contrib/bootstrap5/templates/navigation/menu--main.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array("import" => 7, "macro" => 11, "if" => 13, "set" => 15, "for" => 22);
        static $filters = array("escape" => 21);
        static $functions = array("link" => 47);

        try {
            $this->sandbox->checkSecurity(
                ['import', 'macro', 'if', 'set', 'for'],
                ['escape'],
                ['link']
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
