<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* themes/contrib/bootstrap5/templates/content/node--413.html.twig */
class __TwigTemplate_bf8630c603e00a7ec9b9d99efcf965bc extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 70
        echo "
";
        // line 72
        $context["classes"] = ["node", ("node--type-" . \Drupal\Component\Utility\Html::getClass($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source,         // line 74
($context["node"] ?? null), "bundle", [], "any", false, false, true, 74), 74, $this->source))), ((twig_get_attribute($this->env, $this->source,         // line 75
($context["node"] ?? null), "isPromoted", [], "method", false, false, true, 75)) ? ("node--promoted") : ("")), ((twig_get_attribute($this->env, $this->source,         // line 76
($context["node"] ?? null), "isSticky", [], "method", false, false, true, 76)) ? ("node--sticky") : ("")), (( !twig_get_attribute($this->env, $this->source,         // line 77
($context["node"] ?? null), "isPublished", [], "method", false, false, true, 77)) ? ("node--unpublished") : ("")), ((        // line 78
($context["view_mode"] ?? null)) ? (("node--view-mode-" . \Drupal\Component\Utility\Html::getClass($this->sandbox->ensureToStringAllowed(($context["view_mode"] ?? null), 78, $this->source)))) : (""))];
        // line 81
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->attachLibrary("bootstrap5/node"), "html", null, true);
        echo "
<article";
        // line 82
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", [($context["classes"] ?? null)], "method", false, false, true, 82), 82, $this->source), "html", null, true);
        echo ">

\t";
        // line 84
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_prefix"] ?? null), 84, $this->source), "html", null, true);
        echo "
\t";
        // line 85
        if ((($context["label"] ?? null) &&  !($context["page"] ?? null))) {
            // line 86
            echo "\t\t<h2";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_attributes"] ?? null), 86, $this->source), "html", null, true);
            echo ">
\t\t\t<a href=\"";
            // line 87
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["url"] ?? null), 87, $this->source), "html", null, true);
            echo "\" rel=\"bookmark\">";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["label"] ?? null), 87, $this->source), "html", null, true);
            echo "</a>
\t\t</h2>
\t";
        }
        // line 90
        echo "\t";
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["title_suffix"] ?? null), 90, $this->source), "html", null, true);
        echo "

\t";
        // line 92
        if (($context["display_submitted"] ?? null)) {
            // line 93
            echo "\t\t<footer class=\"node__meta\">
\t\t\t";
            // line 94
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["author_picture"] ?? null), 94, $this->source), "html", null, true);
            echo "
\t\t\t<div";
            // line 95
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["author_attributes"] ?? null), "addClass", ["node__submitted"], "method", false, false, true, 95), 95, $this->source), "html", null, true);
            echo ">
\t\t\t\t";
            // line 96
            echo t("Submitted by
\t\t\t\t@author_name
\t\t\t\ton
\t\t\t\t@date", array("@author_name" =>             // line 97
($context["author_name"] ?? null), "@date" =>             // line 99
($context["date"] ?? null), ));
            // line 100
            echo "\t\t\t\t";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["metadata"] ?? null), 100, $this->source), "html", null, true);
            echo "
\t\t\t</div>
\t\t</footer>
\t";
        }
        // line 104
        echo "
\t<div";
        // line 105
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["content_attributes"] ?? null), "addClass", ["node__content"], "method", false, false, true, 105), 105, $this->source), "html", null, true);
        echo ">
\t\t";
        // line 106
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["content"] ?? null), 106, $this->source), "html", null, true);
        echo "
\t\t<!-- creacorn -->

\t\t<div class=\"container-fluid m-0 text-dark creafree-layout\">
\t\t\t<div
\t\t\t\tclass=\"row\">
\t\t\t\t<!-- 1/3 Left Section for CreaMAKER Download and Description -->
\t\t\t\t<div class=\"col-md-4 col-sm-12 pt-5 fondbleu2 sticky-left\">
\t\t\t\t\t<div class=\"container text-dark text-start\">
\t\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t\t<h1 class=\"fs-1 text-start text-light fw-bold\">
\t\t\t\t\t\t\t\t<svg class=\"bi bi-square-fill\" xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" fill=\"#f29432\" viewbox=\"0 0 16 16\">
\t\t\t\t\t\t\t\t\t<path d=\"M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z\"></path>
\t\t\t\t\t\t\t\t</svg>
\t\t\t\t\t\t\t\tBASIC CreaMAKER
\t\t\t\t\t\t\t</h1>
\t\t\t\t\t\t\t<p class=\"text-light fs-4 text-start mx-3 p-4 pt-2\">
\t\t\t\t\t\t\t\tPrivate patent protecting<br>
your innovation at zero cost<br>
Learn how to make a Basic CreaBOOK
\t\t\t\t\t\t\t</p>
\t\t\t\t\t\t\t<ul class=\"list-unstyled text-light fs-4 text-start p-4 pt-2\"><li class=\"text-light fs-6 text-start mx-3\">Free step by step guide to draft
your private patent<li>
<li class=\"text-light fs-6 text-start mx-3\">Ensure your return on investment
through a long term and world wide protection<li>
<li class=\"text-light fs-6 text-start mx-3 \">Prove the primacy of your authorship
through timestamping<li>

</ul>

<p class=\"text-center\">
<img loading=\"lazy\" src=\"https://creafree.org/imagecreamakerbasic.png\" width=\"291\" height=\"336\" alt=\"creamakerbasic\"></p>
<p class=\"text-center\">
<a href=\"/form/creamaker-basic\" class=\"btn btn-primary btn-sm\" role=\"button\" aria-pressed=\"true\">Download Now</a>
</p>



\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t<!-- 2/3 Right Section with Lists and Text Block -->
\t\t\t\t<div class=\"col-md-8 col-sm-12 pt-2 scrollable-right\">
    <div class=\"px-4 py-3\">
        <div class=\"mb-4\">
            <div class=\"clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item\">
                <div class=\"container py-2\">
                    <div class=\"mb-5\">
                        <h1 class=\"text-start fw-bold display-6 custom-title\">
                            Basic CreaMAKER <br>The essential tool to protect<br>
and showcase your innovation - <br>without traditional patents
                        </h1>
                       
                    </div>
                    <section class=\"mb-5\">
                         <h2 class=\"h4 fw-bold\">
                            Content and Purpose
                        </h2>
                        <div class=\"service-item mb-4\">
                           <p>BASIC CreaMAKER is a simple and structured private patent compatible with the CreaFREE Standard. It allows any creator to formalize his creative idea, prove its originality and timetamp its primacy</p>
                        </div>
                        <div class=\"service-item mb-4\">
                            <p>
                                This private patent is made for entrepreneurs, startup founders, students, searchers, inventors, artists, engineers, companies, NGOs, local authorities — anyone who innovates and needs IP without the costs, delays and complexities of the traditional state patents.
                            </p>
                        </div>
                        <div class=\"service-item mb-4\">
                            <p>
                                In order to finance the necessary research and development required to bring your innovation to market, your startup must obtain the support of venture capitalists. This support depends on the return on the investment (ROI) you can offer. To guarantee ROI, your startup needs efficient intellectual property protecting its innovation.
                            </p>
                        </div>
                         <div class=\"service-item mb-4\">
                            <p>
                                With this new tool, your startup can register its private patent and immediately disclose its protected content to share its attractive return with partners and investors.
                            </p>
                        </div>
                    </section>
                </div>
                  <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        What Will you Get?
                    </h2>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Tutorial:</u></strong> Guide to design your CreaBOOK.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Free download:</u></strong> Access to the tutorial at zero cost.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Step-by-step guide:</u></strong> Describe and prove the authenticity of your claims.
                        </p>
                    </div>
                     <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Primacy proof:</u></strong> Timestamp your CreaBOOK with the provided tool and establish the primacy of your author rights.
                        </p>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                       Why is CreaBOOK Protection Unique?
                    </h2>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Competitive Advantage:</u></strong> The private patent protects all your creative ideas against unfair competitors.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Global market:</u></strong> Anchors your startup property worldwide.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Attract Investors:</u></strong> Long term protection which lasts during all your life plus at least 50 years after your death.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Free, fast and easy to use:</u></strong> Your first CreaBOOK can be written, registered and published within a few hours.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Flexible:</u></strong> Can be upgraded with the assistance of accredited IP coaches.
                        </p>
                    </div>
                    <div class=\"service-item mb-4\">
                        <p>
                            <strong><u>Certified:</u></strong> Once registered and upgraded as GOLD CreaBOOK, the conformity, value and originality of your CreaBOOK can be certified by World creators Society.
                        </p>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        How to Proceed?
                    </h2>
                    <div class=\"steps-container py-3\">
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>1.</strong> <u>Download the Tutorial:</u> Click on the blue button at the end of the blue column.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>2.</strong> <u>Make a copy of the tutorial file:</u> Work offline in complete confidentiality before publishing.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>3.</strong> <u>Refer to documentary evidence:</u> List all your primacy evidence in annexes (sketches, articles, mails, photos, crypted know-how …) and timestamp each of them.
                            </p>
                        </div>
                        <div class=\"step-item mb-4\">
                            <p>
                                <strong>4.</strong> <u>Establish the primacy of your primacy:</u> Timestamp your private patent which includes the list of annexes.
                            </p>
                        </div>
                    </div>
                </section>
                <section class=\"mb-5\">
                    <h2 class=\"h4 fw-bold\">
                        Next Step: Publish Your CreaBOOK
                    </h2>
                    <p>
                        To assert your claims and benefit from the other services of the CreaFREE ecosystem, publish your CreaBOOK in the World creations Register.
                    </p>
                    
                </section>
            </div>
        </div>
    </div>
</div>
\t\t</div>
\t</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["node", "view_mode", "attributes", "title_prefix", "label", "page", "title_attributes", "url", "title_suffix", "display_submitted", "author_picture", "author_attributes", "author_name", "date", "metadata", "content_attributes", "content"]);    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "themes/contrib/bootstrap5/templates/content/node--413.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  118 => 106,  114 => 105,  111 => 104,  103 => 100,  101 => 99,  100 => 97,  96 => 96,  92 => 95,  88 => 94,  85 => 93,  83 => 92,  77 => 90,  69 => 87,  64 => 86,  62 => 85,  58 => 84,  53 => 82,  49 => 81,  47 => 78,  46 => 77,  45 => 76,  44 => 75,  43 => 74,  42 => 72,  39 => 70,);
    }

    public function getSourceContext()
    {
        return new Source("", "themes/contrib/bootstrap5/templates/content/node--413.html.twig", "/var/www/html/web/themes/contrib/bootstrap5/templates/content/node--413.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array("set" => 72, "if" => 85, "trans" => 96);
        static $filters = array("clean_class" => 74, "escape" => 81);
        static $functions = array("attach_library" => 81);

        try {
            $this->sandbox->checkSecurity(
                ['set', 'if', 'trans'],
                ['clean_class', 'escape'],
                ['attach_library']
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
