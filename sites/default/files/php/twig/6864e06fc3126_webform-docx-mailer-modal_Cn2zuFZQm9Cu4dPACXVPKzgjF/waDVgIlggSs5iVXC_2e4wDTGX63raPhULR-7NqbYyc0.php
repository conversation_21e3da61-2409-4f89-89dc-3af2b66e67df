<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* modules/custom/webform_docx_mailer/templates/webform-docx-mailer-modal.html.twig */
class __TwigTemplate_ce705d9adc86b364cff413f933cb1b24 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 10
        echo "<div class=\"webform-docx-mailer-modal\">
  <div class=\"modal-content\">
    <div class=\"webform-custom-header text-center mb-4\">
      <h2 class=\"display-4 text-primary\">BASIC CreaMAKER</h2>
      <p class=\"lead\">Delivery Form</p>
      <p>Get my BASIC CreaMAKER tutorial</p>
      <p class=\"text-start\">
        To receive your BASIC CreaMAKER tutorial by email within the next seconds, please:
      </p>
      <ul class=\"list-unstyled text-start\">
        <li>1. Fill in the fields below</li>
        <li>2. Check the box \"I wish to receive the CreaFREE newsletter\"</li>
        <li>3. Click the white on blue Send button below</li>
      </ul>
    </div>
    
    <div class=\"webform-content\">
      ";
        // line 27
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["form"] ?? null), 27, $this->source), "html", null, true);
        echo "
    </div>
  </div>
</div>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["form"]);    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "modules/custom/webform_docx_mailer/templates/webform-docx-mailer-modal.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  58 => 27,  39 => 10,);
    }

    public function getSourceContext()
    {
        return new Source("", "modules/custom/webform_docx_mailer/templates/webform-docx-mailer-modal.html.twig", "/var/www/html/web/modules/custom/webform_docx_mailer/templates/webform-docx-mailer-modal.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array();
        static $filters = array("escape" => 27);
        static $functions = array();

        try {
            $this->sandbox->checkSecurity(
                [],
                ['escape'],
                []
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
