Drupal Scaffold Files are files that are contained inside drupal/core, but are
installed outside of the core directory (e.g. at the Drupal root).

For now, the scaffold files are being maintained in their original
locations. This is done so that Drupal sites based on the template project
drupal-composer/drupal-project may continue to download these files from the
same URLs they have historically been found at.

Those will be deleted from their original location in a future version.
See https://www.drupal.org/project/drupal/issues/3075954 for follow-on work.
