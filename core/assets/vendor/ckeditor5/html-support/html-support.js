!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{"HTML object":"HTML object"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={90:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});var o=r(609),n=r.n(o)()((function(t){return t[1]}));n.push([t.id,":root{--ck-html-object-embed-unfocused-outline-width:1px}.ck-widget.html-object-embed{background-color:var(--ck-color-base-foreground);font-size:var(--ck-font-size-base);min-width:calc(76px + var(--ck-spacing-standard));padding:var(--ck-spacing-small);padding-top:calc(var(--ck-font-size-tiny) + var(--ck-spacing-large))}.ck-widget.html-object-embed:not(.ck-widget_selected):not(:hover){outline:var(--ck-html-object-embed-unfocused-outline-width) dashed var(--ck-color-widget-blurred-border)}.ck-widget.html-object-embed:before{background:#999;border-radius:0 0 var(--ck-border-radius) var(--ck-border-radius);color:var(--ck-color-base-background);content:attr(data-html-object-embed-label);font-family:var(--ck-font-face);font-size:var(--ck-font-size-tiny);font-style:normal;font-weight:400;left:var(--ck-spacing-standard);padding:calc(var(--ck-spacing-tiny) + var(--ck-html-object-embed-unfocused-outline-width)) var(--ck-spacing-small) var(--ck-spacing-tiny);position:absolute;top:0;transition:background var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve)}.ck-widget.html-object-embed .ck-widget__type-around .ck-widget__type-around__button.ck-widget__type-around__button_before{margin-left:50px}.ck-widget.html-object-embed .html-object-embed__content{pointer-events:none}div.ck-widget.html-object-embed{margin:1em auto}span.ck-widget.html-object-embed{display:inline-block}",""]);const i=n},609:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=t(e);return e[2]?"@media ".concat(e[2]," {").concat(r,"}"):r})).join("")},e.i=function(t,r,o){"string"==typeof t&&(t=[[null,t,""]]);var n={};if(o)for(var i=0;i<this.length;i++){var s=this[i][0];null!=s&&(n[s]=!0)}for(var l=0;l<t.length;l++){var a=[].concat(t[l]);o&&n[a[0]]||(r&&(a[2]?a[2]="".concat(r," and ").concat(a[2]):a[2]=r),e.push(a))}},e}},62:(t,e,r)=>{"use strict";var o,n=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},i=function(){var t={};return function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}t[e]=r}return t[e]}}(),s=[];function l(t){for(var e=-1,r=0;r<s.length;r++)if(s[r].identifier===t){e=r;break}return e}function a(t,e){for(var r={},o=[],n=0;n<t.length;n++){var i=t[n],a=e.base?i[0]+e.base:i[0],c=r[a]||0,u="".concat(a," ").concat(c);r[a]=c+1;var m=l(u),d={css:i[1],media:i[2],sourceMap:i[3]};-1!==m?(s[m].references++,s[m].updater(d)):s.push({identifier:u,updater:p(d,e),references:1}),o.push(u)}return o}function c(t){var e=document.createElement("style"),o=t.attributes||{};if(void 0===o.nonce){var n=r.nc;n&&(o.nonce=n)}if(Object.keys(o).forEach((function(t){e.setAttribute(t,o[t])})),"function"==typeof t.insert)t.insert(e);else{var s=i(t.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(e)}return e}var u,m=(u=[],function(t,e){return u[t]=e,u.filter(Boolean).join("\n")});function d(t,e,r,o){var n=r?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(t.styleSheet)t.styleSheet.cssText=m(e,n);else{var i=document.createTextNode(n),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(i,s[e]):t.appendChild(i)}}function f(t,e,r){var o=r.css,n=r.media,i=r.sourceMap;if(n?t.setAttribute("media",n):t.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleSheet)t.styleSheet.cssText=o;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(o))}}var h=null,b=0;function p(t,e){var r,o,n;if(e.singleton){var i=b++;r=h||(h=c(e)),o=d.bind(null,r,i,!1),n=d.bind(null,r,i,!0)}else r=c(e),o=f.bind(null,r,e),n=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(r)};return o(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;o(t=e)}else n()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=n());var r=a(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var o=0;o<r.length;o++){var n=l(r[o]);s[n].references--}for(var i=a(t,e),c=0;c<r.length;c++){var u=l(r[c]);0===s[u].references&&(s[u].updater(),s.splice(u,1))}r=i}}}},704:(t,e,r)=>{t.exports=r(79)("./src/core.js")},492:(t,e,r)=>{t.exports=r(79)("./src/engine.js")},331:(t,e,r)=>{t.exports=r(79)("./src/enter.js")},209:(t,e,r)=>{t.exports=r(79)("./src/utils.js")},995:(t,e,r)=>{t.exports=r(79)("./src/widget.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function r(o){var n=e[o];if(void 0!==n)return n.exports;var i=e[o]={id:o,exports:{}};return t[o](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var o={};(()=>{"use strict";r.r(o),r.d(o,{DataFilter:()=>On,DataSchema:()=>hn,FullPage:()=>vi,GeneralHtmlSupport:()=>hi,HtmlComment:()=>pi,HtmlPageDataProcessor:()=>gi});var t=r(704),e=r(209),n=r(492),i=r(995);const s=function(){this.__data__=[],this.size=0};const l=function(t,e){return t===e||t!=t&&e!=e};const a=function(t,e){for(var r=t.length;r--;)if(l(t[r][0],e))return r;return-1};var c=Array.prototype.splice;const u=function(t){var e=this.__data__,r=a(e,t);return!(r<0)&&(r==e.length-1?e.pop():c.call(e,r,1),--this.size,!0)};const m=function(t){var e=this.__data__,r=a(e,t);return r<0?void 0:e[r][1]};const d=function(t){return a(this.__data__,t)>-1};const f=function(t,e){var r=this.__data__,o=a(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this};function h(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}h.prototype.clear=s,h.prototype.delete=u,h.prototype.get=m,h.prototype.has=d,h.prototype.set=f;const b=h;const p=function(){this.__data__=new b,this.size=0};const g=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r};const v=function(t){return this.__data__.get(t)};const w=function(t){return this.__data__.has(t)};const y="object"==typeof global&&global&&global.Object===Object&&global;var A="object"==typeof self&&self&&self.Object===Object&&self;const j=y||A||Function("return this")();const E=j.Symbol;var _=Object.prototype,S=_.hasOwnProperty,k=_.toString,O=E?E.toStringTag:void 0;const C=function(t){var e=S.call(t,O),r=t[O];try{t[O]=void 0;var o=!0}catch(t){}var n=k.call(t);return o&&(e?t[O]=r:delete t[O]),n};var x=Object.prototype.toString;const F=function(t){return x.call(t)};var I=E?E.toStringTag:void 0;const P=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":I&&I in Object(t)?C(t):F(t)};const $=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};const T=function(t){if(!$(t))return!1;var e=P(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};const D=j["__core-js_shared__"];var B,R=(B=/[^.]+$/.exec(D&&D.keys&&D.keys.IE_PROTO||""))?"Symbol(src)_1."+B:"";const N=function(t){return!!R&&R in t};var M=Function.prototype.toString;const L=function(t){if(null!=t){try{return M.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var V=/^\[object .+?Constructor\]$/,H=Function.prototype,z=Object.prototype,U=H.toString,W=z.hasOwnProperty,q=RegExp("^"+U.call(W).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const G=function(t){return!(!$(t)||N(t))&&(T(t)?q:V).test(L(t))};const Z=function(t,e){return null==t?void 0:t[e]};const K=function(t,e){var r=Z(t,e);return G(r)?r:void 0};const Y=K(j,"Map");const J=K(Object,"create");const X=function(){this.__data__=J?J(null):{},this.size=0};const Q=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e};var tt=Object.prototype.hasOwnProperty;const et=function(t){var e=this.__data__;if(J){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return tt.call(e,t)?e[t]:void 0};var rt=Object.prototype.hasOwnProperty;const ot=function(t){var e=this.__data__;return J?void 0!==e[t]:rt.call(e,t)};const nt=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=J&&void 0===e?"__lodash_hash_undefined__":e,this};function it(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}it.prototype.clear=X,it.prototype.delete=Q,it.prototype.get=et,it.prototype.has=ot,it.prototype.set=nt;const st=it;const lt=function(){this.size=0,this.__data__={hash:new st,map:new(Y||b),string:new st}};const at=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t};const ct=function(t,e){var r=t.__data__;return at(e)?r["string"==typeof e?"string":"hash"]:r.map};const ut=function(t){var e=ct(this,t).delete(t);return this.size-=e?1:0,e};const mt=function(t){return ct(this,t).get(t)};const dt=function(t){return ct(this,t).has(t)};const ft=function(t,e){var r=ct(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this};function ht(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}ht.prototype.clear=lt,ht.prototype.delete=ut,ht.prototype.get=mt,ht.prototype.has=dt,ht.prototype.set=ft;const bt=ht;const pt=function(t,e){var r=this.__data__;if(r instanceof b){var o=r.__data__;if(!Y||o.length<199)return o.push([t,e]),this.size=++r.size,this;r=this.__data__=new bt(o)}return r.set(t,e),this.size=r.size,this};function gt(t){var e=this.__data__=new b(t);this.size=e.size}gt.prototype.clear=p,gt.prototype.delete=g,gt.prototype.get=v,gt.prototype.has=w,gt.prototype.set=pt;const vt=gt;const wt=function(t,e){for(var r=-1,o=null==t?0:t.length;++r<o&&!1!==e(t[r],r,t););return t};const yt=function(){try{var t=K(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();const At=function(t,e,r){"__proto__"==e&&yt?yt(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r};var jt=Object.prototype.hasOwnProperty;const Et=function(t,e,r){var o=t[e];jt.call(t,e)&&l(o,r)&&(void 0!==r||e in t)||At(t,e,r)};const _t=function(t,e,r,o){var n=!r;r||(r={});for(var i=-1,s=e.length;++i<s;){var l=e[i],a=o?o(r[l],t[l],l,r,t):void 0;void 0===a&&(a=t[l]),n?At(r,l,a):Et(r,l,a)}return r};const St=function(t,e){for(var r=-1,o=Array(t);++r<t;)o[r]=e(r);return o};const kt=function(t){return null!=t&&"object"==typeof t};const Ot=function(t){return kt(t)&&"[object Arguments]"==P(t)};var Ct=Object.prototype,xt=Ct.hasOwnProperty,Ft=Ct.propertyIsEnumerable;const It=Ot(function(){return arguments}())?Ot:function(t){return kt(t)&&xt.call(t,"callee")&&!Ft.call(t,"callee")};const Pt=Array.isArray;const $t=function(){return!1};var Tt="object"==typeof exports&&exports&&!exports.nodeType&&exports,Dt=Tt&&"object"==typeof module&&module&&!module.nodeType&&module,Bt=Dt&&Dt.exports===Tt?j.Buffer:void 0;const Rt=(Bt?Bt.isBuffer:void 0)||$t;var Nt=/^(?:0|[1-9]\d*)$/;const Mt=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&Nt.test(t))&&t>-1&&t%1==0&&t<e};const Lt=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991};var Vt={};Vt["[object Float32Array]"]=Vt["[object Float64Array]"]=Vt["[object Int8Array]"]=Vt["[object Int16Array]"]=Vt["[object Int32Array]"]=Vt["[object Uint8Array]"]=Vt["[object Uint8ClampedArray]"]=Vt["[object Uint16Array]"]=Vt["[object Uint32Array]"]=!0,Vt["[object Arguments]"]=Vt["[object Array]"]=Vt["[object ArrayBuffer]"]=Vt["[object Boolean]"]=Vt["[object DataView]"]=Vt["[object Date]"]=Vt["[object Error]"]=Vt["[object Function]"]=Vt["[object Map]"]=Vt["[object Number]"]=Vt["[object Object]"]=Vt["[object RegExp]"]=Vt["[object Set]"]=Vt["[object String]"]=Vt["[object WeakMap]"]=!1;const Ht=function(t){return kt(t)&&Lt(t.length)&&!!Vt[P(t)]};const zt=function(t){return function(e){return t(e)}};var Ut="object"==typeof exports&&exports&&!exports.nodeType&&exports,Wt=Ut&&"object"==typeof module&&module&&!module.nodeType&&module,qt=Wt&&Wt.exports===Ut&&y.process;const Gt=function(){try{var t=Wt&&Wt.require&&Wt.require("util").types;return t||qt&&qt.binding&&qt.binding("util")}catch(t){}}();var Zt=Gt&&Gt.isTypedArray;const Kt=Zt?zt(Zt):Ht;var Yt=Object.prototype.hasOwnProperty;const Jt=function(t,e){var r=Pt(t),o=!r&&It(t),n=!r&&!o&&Rt(t),i=!r&&!o&&!n&&Kt(t),s=r||o||n||i,l=s?St(t.length,String):[],a=l.length;for(var c in t)!e&&!Yt.call(t,c)||s&&("length"==c||n&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Mt(c,a))||l.push(c);return l};var Xt=Object.prototype;const Qt=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Xt)};const te=function(t,e){return function(r){return t(e(r))}};const ee=te(Object.keys,Object);var re=Object.prototype.hasOwnProperty;const oe=function(t){if(!Qt(t))return ee(t);var e=[];for(var r in Object(t))re.call(t,r)&&"constructor"!=r&&e.push(r);return e};const ne=function(t){return null!=t&&Lt(t.length)&&!T(t)};const ie=function(t){return ne(t)?Jt(t):oe(t)};const se=function(t,e){return t&&_t(e,ie(e),t)};const le=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e};var ae=Object.prototype.hasOwnProperty;const ce=function(t){if(!$(t))return le(t);var e=Qt(t),r=[];for(var o in t)("constructor"!=o||!e&&ae.call(t,o))&&r.push(o);return r};const ue=function(t){return ne(t)?Jt(t,!0):ce(t)};const me=function(t,e){return t&&_t(e,ue(e),t)};var de="object"==typeof exports&&exports&&!exports.nodeType&&exports,fe=de&&"object"==typeof module&&module&&!module.nodeType&&module,he=fe&&fe.exports===de?j.Buffer:void 0,be=he?he.allocUnsafe:void 0;const pe=function(t,e){if(e)return t.slice();var r=t.length,o=be?be(r):new t.constructor(r);return t.copy(o),o};const ge=function(t,e){var r=-1,o=t.length;for(e||(e=Array(o));++r<o;)e[r]=t[r];return e};const ve=function(t,e){for(var r=-1,o=null==t?0:t.length,n=0,i=[];++r<o;){var s=t[r];e(s,r,t)&&(i[n++]=s)}return i};const we=function(){return[]};var ye=Object.prototype.propertyIsEnumerable,Ae=Object.getOwnPropertySymbols;const je=Ae?function(t){return null==t?[]:(t=Object(t),ve(Ae(t),(function(e){return ye.call(t,e)})))}:we;const Ee=function(t,e){return _t(t,je(t),e)};const _e=function(t,e){for(var r=-1,o=e.length,n=t.length;++r<o;)t[n+r]=e[r];return t};const Se=te(Object.getPrototypeOf,Object);const ke=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)_e(e,je(t)),t=Se(t);return e}:we;const Oe=function(t,e){return _t(t,ke(t),e)};const Ce=function(t,e,r){var o=e(t);return Pt(t)?o:_e(o,r(t))};const xe=function(t){return Ce(t,ie,je)};const Fe=function(t){return Ce(t,ue,ke)};const Ie=K(j,"DataView");const Pe=K(j,"Promise");const $e=K(j,"Set");const Te=K(j,"WeakMap");var De="[object Map]",Be="[object Promise]",Re="[object Set]",Ne="[object WeakMap]",Me="[object DataView]",Le=L(Ie),Ve=L(Y),He=L(Pe),ze=L($e),Ue=L(Te),We=P;(Ie&&We(new Ie(new ArrayBuffer(1)))!=Me||Y&&We(new Y)!=De||Pe&&We(Pe.resolve())!=Be||$e&&We(new $e)!=Re||Te&&We(new Te)!=Ne)&&(We=function(t){var e=P(t),r="[object Object]"==e?t.constructor:void 0,o=r?L(r):"";if(o)switch(o){case Le:return Me;case Ve:return De;case He:return Be;case ze:return Re;case Ue:return Ne}return e});const qe=We;var Ge=Object.prototype.hasOwnProperty;const Ze=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&Ge.call(t,"index")&&(r.index=t.index,r.input=t.input),r};const Ke=j.Uint8Array;const Ye=function(t){var e=new t.constructor(t.byteLength);return new Ke(e).set(new Ke(t)),e};const Je=function(t,e){var r=e?Ye(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)};var Xe=/\w*$/;const Qe=function(t){var e=new t.constructor(t.source,Xe.exec(t));return e.lastIndex=t.lastIndex,e};var tr=E?E.prototype:void 0,er=tr?tr.valueOf:void 0;const rr=function(t){return er?Object(er.call(t)):{}};const or=function(t,e){var r=e?Ye(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)};const nr=function(t,e,r){var o=t.constructor;switch(e){case"[object ArrayBuffer]":return Ye(t);case"[object Boolean]":case"[object Date]":return new o(+t);case"[object DataView]":return Je(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return or(t,r);case"[object Map]":case"[object Set]":return new o;case"[object Number]":case"[object String]":return new o(t);case"[object RegExp]":return Qe(t);case"[object Symbol]":return rr(t)}};var ir=Object.create;const sr=function(){function t(){}return function(e){if(!$(e))return{};if(ir)return ir(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();const lr=function(t){return"function"!=typeof t.constructor||Qt(t)?{}:sr(Se(t))};const ar=function(t){return kt(t)&&"[object Map]"==qe(t)};var cr=Gt&&Gt.isMap;const ur=cr?zt(cr):ar;const mr=function(t){return kt(t)&&"[object Set]"==qe(t)};var dr=Gt&&Gt.isSet;const fr=dr?zt(dr):mr;var hr="[object Arguments]",br="[object Function]",pr="[object Object]",gr={};gr[hr]=gr["[object Array]"]=gr["[object ArrayBuffer]"]=gr["[object DataView]"]=gr["[object Boolean]"]=gr["[object Date]"]=gr["[object Float32Array]"]=gr["[object Float64Array]"]=gr["[object Int8Array]"]=gr["[object Int16Array]"]=gr["[object Int32Array]"]=gr["[object Map]"]=gr["[object Number]"]=gr[pr]=gr["[object RegExp]"]=gr["[object Set]"]=gr["[object String]"]=gr["[object Symbol]"]=gr["[object Uint8Array]"]=gr["[object Uint8ClampedArray]"]=gr["[object Uint16Array]"]=gr["[object Uint32Array]"]=!0,gr["[object Error]"]=gr[br]=gr["[object WeakMap]"]=!1;const vr=function t(e,r,o,n,i,s){var l,a=1&r,c=2&r,u=4&r;if(o&&(l=i?o(e,n,i,s):o(e)),void 0!==l)return l;if(!$(e))return e;var m=Pt(e);if(m){if(l=Ze(e),!a)return ge(e,l)}else{var d=qe(e),f=d==br||"[object GeneratorFunction]"==d;if(Rt(e))return pe(e,a);if(d==pr||d==hr||f&&!i){if(l=c||f?{}:lr(e),!a)return c?Oe(e,me(l,e)):Ee(e,se(l,e))}else{if(!gr[d])return i?e:{};l=nr(e,d,a)}}s||(s=new vt);var h=s.get(e);if(h)return h;s.set(e,l),fr(e)?e.forEach((function(n){l.add(t(n,r,o,n,e,s))})):ur(e)&&e.forEach((function(n,i){l.set(i,t(n,r,o,i,e,s))}));var b=m?void 0:(u?c?Fe:xe:c?ue:ie)(e);return wt(b||e,(function(n,i){b&&(n=e[i=n]),Et(l,i,t(n,r,o,i,e,s))})),l};const wr=function(t){return vr(t,5)};const yr=function(t,e,r,o){var n=-1,i=null==t?0:t.length;for(o&&i&&(r=t[++n]);++n<i;)r=e(r,t[n],n,t);return r};const Ar=function(t){return function(e){return null==t?void 0:t[e]}}({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});const jr=function(t,e){for(var r=-1,o=null==t?0:t.length,n=Array(o);++r<o;)n[r]=e(t[r],r,t);return n};const Er=function(t){return"symbol"==typeof t||kt(t)&&"[object Symbol]"==P(t)};var _r=E?E.prototype:void 0,Sr=_r?_r.toString:void 0;const kr=function t(e){if("string"==typeof e)return e;if(Pt(e))return jr(e,t)+"";if(Er(e))return Sr?Sr.call(e):"";var r=e+"";return"0"==r&&1/e==-Infinity?"-0":r};const Or=function(t){return null==t?"":kr(t)};var Cr=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,xr=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");const Fr=function(t){return(t=Or(t))&&t.replace(Cr,Ar).replace(xr,"")};var Ir=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;const Pr=function(t){return t.match(Ir)||[]};var $r=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;const Tr=function(t){return $r.test(t)};var Dr="\\ud800-\\udfff",Br="\\u2700-\\u27bf",Rr="a-z\\xdf-\\xf6\\xf8-\\xff",Nr="A-Z\\xc0-\\xd6\\xd8-\\xde",Mr="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Lr="["+Mr+"]",Vr="\\d+",Hr="["+Br+"]",zr="["+Rr+"]",Ur="[^"+Dr+Mr+Vr+Br+Rr+Nr+"]",Wr="(?:\\ud83c[\\udde6-\\uddff]){2}",qr="[\\ud800-\\udbff][\\udc00-\\udfff]",Gr="["+Nr+"]",Zr="(?:"+zr+"|"+Ur+")",Kr="(?:"+Gr+"|"+Ur+")",Yr="(?:['’](?:d|ll|m|re|s|t|ve))?",Jr="(?:['’](?:D|LL|M|RE|S|T|VE))?",Xr="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",Qr="[\\ufe0e\\ufe0f]?",to=Qr+Xr+("(?:\\u200d(?:"+["[^"+Dr+"]",Wr,qr].join("|")+")"+Qr+Xr+")*"),eo="(?:"+[Hr,Wr,qr].join("|")+")"+to,ro=RegExp([Gr+"?"+zr+"+"+Yr+"(?="+[Lr,Gr,"$"].join("|")+")",Kr+"+"+Jr+"(?="+[Lr,Gr+Zr,"$"].join("|")+")",Gr+"?"+Zr+"+"+Yr,Gr+"+"+Jr,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Vr,eo].join("|"),"g");const oo=function(t){return t.match(ro)||[]};const no=function(t,e,r){return t=Or(t),void 0===(e=r?void 0:e)?Tr(t)?oo(t):Pr(t):t.match(e)||[]};var io=RegExp("['’]","g");const so=function(t){return function(e){return yr(no(Fr(e).replace(io,"")),t,"")}};const lo=function(t,e,r){var o=-1,n=t.length;e<0&&(e=-e>n?0:n+e),(r=r>n?n:r)<0&&(r+=n),n=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(n);++o<n;)i[o]=t[o+e];return i};const ao=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:lo(t,e,r)};var co=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");const uo=function(t){return co.test(t)};const mo=function(t){return t.split("")};var fo="\\ud800-\\udfff",ho="["+fo+"]",bo="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",po="\\ud83c[\\udffb-\\udfff]",go="[^"+fo+"]",vo="(?:\\ud83c[\\udde6-\\uddff]){2}",wo="[\\ud800-\\udbff][\\udc00-\\udfff]",yo="(?:"+bo+"|"+po+")"+"?",Ao="[\\ufe0e\\ufe0f]?",jo=Ao+yo+("(?:\\u200d(?:"+[go,vo,wo].join("|")+")"+Ao+yo+")*"),Eo="(?:"+[go+bo+"?",bo,vo,wo,ho].join("|")+")",_o=RegExp(po+"(?="+po+")|"+Eo+jo,"g");const So=function(t){return t.match(_o)||[]};const ko=function(t){return uo(t)?So(t):mo(t)};const Oo=function(t){return function(e){e=Or(e);var r=uo(e)?ko(e):void 0,o=r?r[0]:e.charAt(0),n=r?ao(r,1).join(""):e.slice(1);return o[t]()+n}}("toUpperCase");const Co=so((function(t,e,r){return t+(r?" ":"")+Oo(e)}));function xo(t,e,r,o){e&&function(t,e,r){if(e.attributes)for(const[o]of Object.entries(e.attributes))t.removeAttribute(o,r);if(e.styles)for(const o of Object.keys(e.styles))t.removeStyle(o,r);e.classes&&t.removeClass(e.classes,r)}(t,e,o),r&&Fo(t,r,o)}function Fo(t,e,r){if(e.attributes)for(const[o,n]of Object.entries(e.attributes))t.setAttribute(o,n,r);e.styles&&t.setStyle(e.styles,r),e.classes&&t.addClass(e.classes,r)}function Io(t,e,r,o,n){const i=e.getAttribute(r),s={};for(const t of["attributes","styles","classes"]){if(t!=o){i&&i[t]&&(s[t]=i[t]);continue}if("classes"==o){const e=new Set(i&&i.classes||[]);n(e),e.size&&(s[t]=Array.from(e));continue}const e=new Map(Object.entries(i&&i[t]||{}));n(e),e.size&&(s[t]=Object.fromEntries(e))}Object.keys(s).length?e.is("documentSelection")?t.setSelectionAttribute(r,s):t.setAttribute(r,s,e):i&&(e.is("documentSelection")?t.removeSelectionAttribute(r):t.removeAttribute(r,e))}function Po(t){return`html${e=t,Co(e).replace(/ /g,"")}Attributes`;var e}function $o({model:t}){return(e,r)=>r.writer.createElement(t,{htmlContent:e.getCustomProperty("$rawContent")})}function To(t,{view:e,isInline:r}){const o=t.t;return(t,{writer:n})=>{const s=o("HTML object"),l=Do(e,t,n),a=t.getAttribute(Po(e));n.addClass("html-object-embed__content",l),a&&Fo(n,a,l);const c=n.createContainerElement(r?"span":"div",{class:"html-object-embed","data-html-object-embed-label":s},l);return(0,i.toWidget)(c,n,{label:s})}}function Do(t,e,r){return r.createRawElement(t,null,((t,r)=>{r.setContentOf(t,e.getAttribute("htmlContent"))}))}function Bo({view:t,model:e,allowEmpty:r},o){return e=>{e.on(`element:${t}`,((t,e,i)=>{let s=o.processViewAttributes(e.viewItem,i);if(s||i.consumable.test(e.viewItem,{name:!0})){if(s=s||{},i.consumable.consume(e.viewItem,{name:!0}),e.modelRange||(e=Object.assign(e,i.convertChildren(e.viewItem,e.modelCursor))),r&&e.modelRange.isCollapsed&&Object.keys(s).length){const t=i.writer.createElement("htmlEmptyElement");if(!i.safeInsert(t,e.modelCursor))return;const r=i.getSplitParts(t);return e.modelRange=i.writer.createRange(e.modelRange.start,i.writer.createPositionAfter(r[r.length-1])),i.updateConversionResult(t,e),void n(t,s,i)}for(const t of e.modelRange.getItems())n(t,s,i)}}),{priority:"low"})};function n(t,r,o){if(o.schema.checkAttribute(t,e)){const n=function(t,e){const r=wr(t);let o="attributes";for(o in e)r[o]="classes"==o?Array.from(new Set([...t[o]||[],...e[o]])):{...t[o],...e[o]};return r}(r,t.getAttribute(e)||{});o.writer.setAttribute(e,n,t)}}}function Ro({model:t,view:e},r){return(o,{writer:n,consumable:s})=>{if(!o.hasAttribute(t))return null;const l=n.createContainerElement(e),a=o.getAttribute(t);return s.consume(o,`attribute:${t}`),Fo(n,a,l),l.getFillerOffset=()=>null,r?(0,i.toWidget)(l,n):l}}function No({priority:t,view:e}){return(r,o)=>{if(!r)return;const{writer:n}=o,i=n.createAttributeElement(e,null,{priority:t});return Fo(n,r,i),i}}function Mo({view:t},e){return r=>{r.on(`element:${t}`,((t,r,o)=>{if(!r.modelRange||r.modelRange.isCollapsed)return;const n=e.processViewAttributes(r.viewItem,o);n&&o.writer.setAttribute(Po(r.viewItem.name),n,r.modelRange)}),{priority:"low"})}}function Lo({view:t,model:e}){return r=>{r.on(`attribute:${Po(t)}:${e}`,((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e;xo(r.writer,o,n,r.mapper.toViewElement(e.item))}))}}const Vo=[{model:"codeBlock",view:"pre"},{model:"paragraph",view:"p"},{model:"blockQuote",view:"blockquote"},{model:"listItem",view:"li"},{model:"pageBreak",view:"div"},{model:"rawHtml",view:"div"},{model:"table",view:"table"},{model:"tableRow",view:"tr"},{model:"tableCell",view:"td"},{model:"tableCell",view:"th"},{model:"tableColumnGroup",view:"colgroup"},{model:"tableColumn",view:"col"},{model:"caption",view:"caption"},{model:"caption",view:"figcaption"},{model:"imageBlock",view:"img"},{model:"imageInline",view:"img"},{model:"htmlP",view:"p",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlBlockquote",view:"blockquote",modelSchema:{inheritAllFrom:"$container"}},{model:"htmlTable",view:"table",modelSchema:{allowWhere:"$block",isBlock:!0}},{model:"htmlTbody",view:"tbody",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlThead",view:"thead",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlTfoot",view:"tfoot",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlCaption",view:"caption",modelSchema:{allowIn:"htmlTable",allowChildren:"$text",isBlock:!1}},{model:"htmlColgroup",view:"colgroup",modelSchema:{allowIn:"htmlTable",allowChildren:"col",isBlock:!1}},{model:"htmlCol",view:"col",modelSchema:{allowIn:"htmlColgroup",isBlock:!1}},{model:"htmlTr",view:"tr",modelSchema:{allowIn:["htmlTable","htmlThead","htmlTbody"],isLimit:!0}},{model:"htmlTd",view:"td",modelSchema:{allowIn:"htmlTr",allowContentOf:"$container",isLimit:!0,isBlock:!1}},{model:"htmlTh",view:"th",modelSchema:{allowIn:"htmlTr",allowContentOf:"$container",isLimit:!0,isBlock:!1}},{model:"htmlFigure",view:"figure",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlFigcaption",view:"figcaption",modelSchema:{allowIn:"htmlFigure",allowChildren:"$text",isBlock:!1}},{model:"htmlAddress",view:"address",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlAside",view:"aside",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlMain",view:"main",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlDetails",view:"details",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlSummary",view:"summary",modelSchema:{allowChildren:"$text",allowIn:"htmlDetails",isBlock:!1}},{model:"htmlDiv",view:"div",paragraphLikeModel:"htmlDivParagraph",modelSchema:{inheritAllFrom:"$container"}},{model:"htmlFieldset",view:"fieldset",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlLegend",view:"legend",modelSchema:{allowIn:"htmlFieldset",allowChildren:"$text"}},{model:"htmlHeader",view:"header",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlFooter",view:"footer",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlForm",view:"form",modelSchema:{inheritAllFrom:"$container",isBlock:!0}},{model:"htmlHgroup",view:"hgroup",modelSchema:{allowChildren:["htmlH1","htmlH2","htmlH3","htmlH4","htmlH5","htmlH6"],isBlock:!1}},{model:"htmlH1",view:"h1",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH2",view:"h2",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH3",view:"h3",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH4",view:"h4",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH5",view:"h5",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH6",view:"h6",modelSchema:{inheritAllFrom:"$block"}},{model:"$htmlList",modelSchema:{allowWhere:"$container",allowChildren:["$htmlList","htmlLi"],isBlock:!1}},{model:"htmlDir",view:"dir",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlMenu",view:"menu",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlUl",view:"ul",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlOl",view:"ol",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlLi",view:"li",modelSchema:{allowIn:"$htmlList",allowChildren:"$text",isBlock:!1}},{model:"htmlPre",view:"pre",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlArticle",view:"article",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlSection",view:"section",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlNav",view:"nav",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlDivDl",view:"div",modelSchema:{allowChildren:["htmlDt","htmlDd"],allowIn:"htmlDl"}},{model:"htmlDl",view:"dl",modelSchema:{allowWhere:"$container",allowChildren:["htmlDt","htmlDd","htmlDivDl"],isBlock:!1}},{model:"htmlDt",view:"dt",modelSchema:{allowChildren:"$block",isBlock:!1}},{model:"htmlDd",view:"dd",modelSchema:{allowChildren:"$block",isBlock:!1}},{model:"htmlCenter",view:"center",modelSchema:{inheritAllFrom:"$container",isBlock:!1}}],Ho=[{model:"htmlLiAttributes",view:"li",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlOlAttributes",view:"ol",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlUlAttributes",view:"ul",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlFigureAttributes",view:"figure",appliesToBlock:"table"},{model:"htmlTheadAttributes",view:"thead",appliesToBlock:"table"},{model:"htmlTbodyAttributes",view:"tbody",appliesToBlock:"table"},{model:"htmlFigureAttributes",view:"figure",appliesToBlock:"imageBlock"},{model:"htmlAcronym",view:"acronym",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlTt",view:"tt",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlFont",view:"font",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlTime",view:"time",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlVar",view:"var",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBig",view:"big",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSmall",view:"small",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSamp",view:"samp",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlQ",view:"q",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlOutput",view:"output",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlKbd",view:"kbd",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBdi",view:"bdi",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBdo",view:"bdo",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlAbbr",view:"abbr",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlA",view:"a",priority:5,coupledAttribute:"linkHref"},{model:"htmlStrong",view:"strong",coupledAttribute:"bold",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlB",view:"b",coupledAttribute:"bold",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlI",view:"i",coupledAttribute:"italic",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlEm",view:"em",coupledAttribute:"italic",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlS",view:"s",coupledAttribute:"strikethrough",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlDel",view:"del",coupledAttribute:"strikethrough",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlIns",view:"ins",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlU",view:"u",coupledAttribute:"underline",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSub",view:"sub",coupledAttribute:"subscript",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSup",view:"sup",coupledAttribute:"superscript",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlCode",view:"code",coupledAttribute:"code",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlMark",view:"mark",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSpan",view:"span",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlCite",view:"cite",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlLabel",view:"label",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlDfn",view:"dfn",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlObject",view:"object",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlIframe",view:"iframe",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlInput",view:"input",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlButton",view:"button",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlTextarea",view:"textarea",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlSelect",view:"select",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlVideo",view:"video",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlEmbed",view:"embed",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlOembed",view:"oembed",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlAudio",view:"audio",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlImg",view:"img",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlCanvas",view:"canvas",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlMeter",view:"meter",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlProgress",view:"progress",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlScript",view:"script",modelSchema:{allowWhere:["$text","$block"],isInline:!0}},{model:"htmlStyle",view:"style",modelSchema:{allowWhere:["$text","$block"],isInline:!0}},{model:"htmlCustomElement",view:"$customElement",modelSchema:{allowWhere:["$text","$block"],allowAttributesOf:"$inlineObject",isInline:!0}}];const zo=function(t,e,r){(void 0!==r&&!l(t[e],r)||void 0===r&&!(e in t))&&At(t,e,r)};const Uo=function(t){return function(e,r,o){for(var n=-1,i=Object(e),s=o(e),l=s.length;l--;){var a=s[t?l:++n];if(!1===r(i[a],a,i))break}return e}}();const Wo=function(t){return kt(t)&&ne(t)};var qo=Function.prototype,Go=Object.prototype,Zo=qo.toString,Ko=Go.hasOwnProperty,Yo=Zo.call(Object);const Jo=function(t){if(!kt(t)||"[object Object]"!=P(t))return!1;var e=Se(t);if(null===e)return!0;var r=Ko.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&Zo.call(r)==Yo};const Xo=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};const Qo=function(t){return _t(t,ue(t))};const tn=function(t,e,r,o,n,i,s){var l=Xo(t,r),a=Xo(e,r),c=s.get(a);if(c)zo(t,r,c);else{var u=i?i(l,a,r+"",t,e,s):void 0,m=void 0===u;if(m){var d=Pt(a),f=!d&&Rt(a),h=!d&&!f&&Kt(a);u=a,d||f||h?Pt(l)?u=l:Wo(l)?u=ge(l):f?(m=!1,u=pe(a,!0)):h?(m=!1,u=or(a,!0)):u=[]:Jo(a)||It(a)?(u=l,It(l)?u=Qo(l):$(l)&&!T(l)||(u=lr(a))):m=!1}m&&(s.set(a,u),n(u,a,o,i,s),s.delete(a)),zo(t,r,u)}};const en=function t(e,r,o,n,i){e!==r&&Uo(r,(function(s,l){if(i||(i=new vt),$(s))tn(e,r,l,o,t,n,i);else{var a=n?n(Xo(e,l),s,l+"",e,r,i):void 0;void 0===a&&(a=s),zo(e,l,a)}}),ue)};const rn=function(t){return t};const on=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)};var nn=Math.max;const sn=function(t,e,r){return e=nn(void 0===e?t.length-1:e,0),function(){for(var o=arguments,n=-1,i=nn(o.length-e,0),s=Array(i);++n<i;)s[n]=o[e+n];n=-1;for(var l=Array(e+1);++n<e;)l[n]=o[n];return l[e]=r(s),on(t,this,l)}};const ln=function(t){return function(){return t}};const an=yt?function(t,e){return yt(t,"toString",{configurable:!0,enumerable:!1,value:ln(e),writable:!0})}:rn;var cn=Date.now;const un=function(t){var e=0,r=0;return function(){var o=cn(),n=16-(o-r);if(r=o,n>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(an);const mn=function(t,e){return un(sn(t,e,rn),t+"")};const dn=function(t,e,r){if(!$(r))return!1;var o=typeof e;return!!("number"==o?ne(r)&&Mt(e,r.length):"string"==o&&e in r)&&l(r[e],t)};const fn=function(t){return mn((function(e,r){var o=-1,n=r.length,i=n>1?r[n-1]:void 0,s=n>2?r[2]:void 0;for(i=t.length>3&&"function"==typeof i?(n--,i):void 0,s&&dn(r[0],r[1],s)&&(i=n<3?void 0:i,n=1),e=Object(e);++o<n;){var l=r[o];l&&t(e,l,o,i)}return e}))}((function(t,e,r,o){en(t,e,r,o)}));class hn extends t.Plugin{constructor(){super(...arguments),this._definitions=[]}static get pluginName(){return"DataSchema"}init(){for(const t of Vo)this.registerBlockElement(t);for(const t of Ho)this.registerInlineElement(t)}registerBlockElement(t){this._definitions.push({...t,isBlock:!0})}registerInlineElement(t){this._definitions.push({...t,isInline:!0})}extendBlockElement(t){this._extendDefinition({...t,isBlock:!0})}extendInlineElement(t){this._extendDefinition({...t,isInline:!0})}getDefinitionsForView(t,e=!1){const r=new Set;for(const o of this._getMatchingViewDefinitions(t)){if(e)for(const t of this._getReferences(o.model))r.add(t);r.add(o)}return r}getDefinitionsForModel(t){return this._definitions.filter((e=>e.model==t))}_getMatchingViewDefinitions(t){return this._definitions.filter((e=>e.view&&function(t,e){if("string"==typeof t)return t===e;if(t instanceof RegExp)return t.test(e);return!1}(t,e.view)))}*_getReferences(t){const r=["inheritAllFrom","inheritTypesFrom","allowWhere","allowContentOf","allowAttributesOf"],o=this._definitions.filter((e=>e.model==t));for(const{modelSchema:n}of o)if(n)for(const o of r)for(const r of(0,e.toArray)(n[o]||[])){const e=this._definitions.filter((t=>t.model==r));for(const o of e)r!==t&&(yield*this._getReferences(o.model),yield o)}}_extendDefinition(t){const e=Array.from(this._definitions.entries()).filter((([,e])=>e.model==t.model));if(0!=e.length)for(const[r,o]of e)this._definitions[r]=fn({},o,t,((t,e)=>Array.isArray(t)?t.concat(e):void 0));else this._definitions.push(t)}}const bn=function(t,e,r,o){for(var n=t.length,i=r+(o?1:-1);o?i--:++i<n;)if(e(t[i],i,t))return i;return-1};const pn=function(t){return t!=t};const gn=function(t,e,r){for(var o=r-1,n=t.length;++o<n;)if(t[o]===e)return o;return-1};const vn=function(t,e,r){return e==e?gn(t,e,r):bn(t,pn,r)};const wn=function(t,e,r,o){for(var n=r-1,i=t.length;++n<i;)if(o(t[n],e))return n;return-1};var yn=Array.prototype.splice;const An=function(t,e,r,o){var n=o?wn:vn,i=-1,s=e.length,l=t;for(t===e&&(e=ge(e)),r&&(l=jr(t,zt(r)));++i<s;)for(var a=0,c=e[i],u=r?r(c):c;(a=n(l,u,a,o))>-1;)l!==t&&yn.call(l,a,1),yn.call(t,a,1);return t};const jn=mn((function(t,e){return t&&t.length&&e&&e.length?An(t,e):t}));var En=r(62),_n=r.n(En),Sn=r(90),kn={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_n()(Sn.Z,kn);Sn.Z.locals;class On extends t.Plugin{constructor(t){super(t),this._dataSchema=t.plugins.get("DataSchema"),this._allowedAttributes=new n.Matcher,this._disallowedAttributes=new n.Matcher,this._allowedElements=new Set,this._disallowedElements=new Set,this._dataInitialized=!1,this._coupledAttributes=null,this._registerElementsAfterInit(),this._registerElementHandlers(),this._registerCoupledAttributesPostFixer(),this._registerAssociatedHtmlAttributesPostFixer()}static get pluginName(){return"DataFilter"}static get requires(){return[hn,i.Widget]}loadAllowedConfig(t){for(const e of t){const t=e.name||/[\s\S]+/,r=Pn(e);this.allowElement(t),r.forEach((t=>this.allowAttributes(t)))}}loadDisallowedConfig(t){for(const e of t){const t=e.name||/[\s\S]+/,r=Pn(e);0==r.length?this.disallowElement(t):r.forEach((t=>this.disallowAttributes(t)))}}loadAllowedEmptyElementsConfig(t){for(const e of t)this.allowEmptyElement(e)}allowElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!0))this._addAllowedElement(e),this._coupledAttributes=null}disallowElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!1))this._disallowedElements.add(e.view)}allowEmptyElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!0))e.isInline&&this._dataSchema.extendInlineElement({...e,allowEmpty:!0})}allowAttributes(t){this._allowedAttributes.add(t)}disallowAttributes(t){this._disallowedAttributes.add(t)}processViewAttributes(t,e){return Cn(t,e,this._disallowedAttributes),Cn(t,e,this._allowedAttributes)}_addAllowedElement(t){if(!this._allowedElements.has(t)){if(this._allowedElements.add(t),"appliesToBlock"in t&&"string"==typeof t.appliesToBlock)for(const e of this._dataSchema.getDefinitionsForModel(t.appliesToBlock))e.isBlock&&this._addAllowedElement(e);this._dataInitialized&&this.editor.data.once("set",(()=>{this._fireRegisterEvent(t)}),{priority:e.priorities.highest+1})}}_registerElementsAfterInit(){this.editor.data.on("init",(()=>{this._dataInitialized=!0;for(const t of this._allowedElements)this._fireRegisterEvent(t)}),{priority:e.priorities.highest+1})}_registerElementHandlers(){this.on("register",((t,r)=>{const o=this.editor.model.schema;if(r.isObject&&!o.isRegistered(r.model))this._registerObjectElement(r);else if(r.isBlock)this._registerBlockElement(r);else{if(!r.isInline)throw new e.CKEditorError("data-filter-invalid-definition",null,r);this._registerInlineElement(r)}t.stop()}),{priority:"lowest"})}_registerCoupledAttributesPostFixer(){const t=this.editor.model,e=t.document.selection;t.document.registerPostFixer((e=>{const r=t.document.differ.getChanges();let o=!1;const n=this._getCoupledAttributesMap();for(const t of r){if("attribute"!=t.type||null!==t.attributeNewValue)continue;const r=n.get(t.attributeKey);if(r)for(const{item:n}of t.range.getWalker({shallow:!0}))for(const t of r)n.hasAttribute(t)&&(e.removeAttribute(t,n),o=!0)}return o})),this.listenTo(e,"change:attribute",((r,{attributeKeys:o})=>{const n=new Set,i=this._getCoupledAttributesMap();for(const t of o){if(e.hasAttribute(t))continue;const r=i.get(t);if(r)for(const t of r)e.hasAttribute(t)&&n.add(t)}0!=n.size&&t.change((t=>{for(const e of n)t.removeSelectionAttribute(e)}))}))}_registerAssociatedHtmlAttributesPostFixer(){const t=this.editor.model;t.document.registerPostFixer((e=>{const r=t.document.differ.getChanges();let o=!1;for(const n of r)if("insert"===n.type&&"$text"!==n.name)for(const r of n.attributes.keys())r.startsWith("html")&&r.endsWith("Attributes")&&(t.schema.checkAttribute(n.name,r)||(e.removeAttribute(r,n.position.nodeAfter),o=!0));return o}))}_getCoupledAttributesMap(){if(this._coupledAttributes)return this._coupledAttributes;this._coupledAttributes=new Map;for(const t of this._allowedElements)if(t.coupledAttribute&&t.model){const e=this._coupledAttributes.get(t.coupledAttribute);e?e.push(t.model):this._coupledAttributes.set(t.coupledAttribute,[t.model])}return this._coupledAttributes}_fireRegisterEvent(t){t.view&&this._disallowedElements.has(t.view)||this.fire(t.view?`register:${t.view}`:"register",t)}_registerObjectElement(t){const r=this.editor,o=r.model.schema,n=r.conversion,{view:i,model:s}=t;o.register(s,t.modelSchema),i&&(o.extend(t.model,{allowAttributes:[Po(i),"htmlContent"]}),r.data.registerRawContentMatcher({name:i}),n.for("upcast").elementToElement({view:i,model:$o(t),converterPriority:e.priorities.low+2}),n.for("upcast").add(Mo(t,this)),n.for("editingDowncast").elementToStructure({model:{name:s,attributes:[Po(i)]},view:To(r,t)}),n.for("dataDowncast").elementToElement({model:s,view:(t,{writer:e})=>Do(i,t,e)}),n.for("dataDowncast").add(Lo(t)))}_registerBlockElement(t){const r=this.editor,o=r.model.schema,n=r.conversion,{view:i,model:s}=t;if(!o.isRegistered(t.model)){if(o.register(t.model,t.modelSchema),!i)return;n.for("upcast").elementToElement({model:s,view:i,converterPriority:e.priorities.low+2}),n.for("downcast").elementToElement({model:s,view:i})}i&&(o.extend(t.model,{allowAttributes:Po(i)}),n.for("upcast").add(Mo(t,this)),n.for("downcast").add(Lo(t)))}_registerInlineElement(t){const e=this.editor,r=e.model.schema,o=e.conversion,n=t.model;t.appliesToBlock||(r.extend("$text",{allowAttributes:n}),t.attributeProperties&&r.setAttributeProperties(n,t.attributeProperties),o.for("upcast").add(Bo(t,this)),o.for("downcast").attributeToElement({model:n,view:No(t)}),t.allowEmpty&&(r.setAttributeProperties(n,{copyFromObject:!1}),r.isRegistered("htmlEmptyElement")||r.register("htmlEmptyElement",{inheritAllFrom:"$inlineObject"}),e.data.htmlProcessor.domConverter.registerInlineObjectMatcher((e=>e.name==t.view&&e.isEmpty&&Array.from(e.getAttributeKeys()).length?{name:!0}:null)),o.for("editingDowncast").elementToElement({model:"htmlEmptyElement",view:Ro(t,!0)}),o.for("dataDowncast").elementToElement({model:"htmlEmptyElement",view:Ro(t)})))}}function Cn(t,r,o){const n=function(t,{consumable:e},r){const o=r.matchAll(t)||[],n=[];for(const r of o)xn(e,t,r),delete r.match.name,e.consume(t,r.match),n.push(r);return n}(t,r,o),{attributes:i,styles:s,classes:l}=function(t){const e={attributes:new Set,classes:new Set,styles:new Set};for(const r of t)for(const t in e){(r.match[t]||[]).forEach((r=>e[t].add(r)))}return e}(n),a={};if(i.size)for(const t of i)(0,e.isValidAttributeName)(t)||i.delete(t);return i.size&&(a.attributes=Fn(i,(e=>t.getAttribute(e)))),s.size&&(a.styles=Fn(s,(e=>t.getStyle(e)))),l.size&&(a.classes=Array.from(l)),Object.keys(a).length?a:null}function xn(t,e,r){for(const o of["attributes","classes","styles"]){const n=r.match[o];if(n)for(const r of Array.from(n))t.test(e,{[o]:[r]})||jn(n,r)}}function Fn(t,e){const r={};for(const o of t){void 0!==e(o)&&(r[o]=e(o))}return r}function In(t,e){const{name:r}=t,o=t[e];return Jo(o)?Object.entries(o).map((([t,o])=>({name:r,[e]:{[t]:o}}))):Array.isArray(o)?o.map((t=>({name:r,[e]:[t]}))):[t]}function Pn(t){const{name:e,attributes:r,classes:o,styles:n}=t,i=[];return r&&i.push(...In({name:e,attributes:r},"attributes")),o&&i.push(...In({name:e,classes:o},"classes")),n&&i.push(...In({name:e,styles:n},"styles")),i}class $n extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"CodeBlockElementSupport"}init(){if(!this.editor.plugins.has("CodeBlockEditing"))return;const t=this.editor.plugins.get(On);t.on("register:pre",((e,r)=>{if("codeBlock"!==r.model)return;const o=this.editor,n=o.model.schema,i=o.conversion;n.extend("codeBlock",{allowAttributes:["htmlPreAttributes","htmlContentAttributes"]}),i.for("upcast").add(function(t){return e=>{e.on("element:code",((e,r,o)=>{const n=r.viewItem,i=n.parent;function s(e,n){const i=t.processViewAttributes(e,o);i&&o.writer.setAttribute(n,i,r.modelRange)}i&&i.is("element","pre")&&(s(i,"htmlPreAttributes"),s(n,"htmlContentAttributes"))}),{priority:"low"})}}(t)),i.for("downcast").add((t=>{t.on("attribute:htmlPreAttributes:codeBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item).parent;xo(r.writer,o,n,i)})),t.on("attribute:htmlContentAttributes:codeBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item);xo(r.writer,o,n,i)}))})),e.stop()}))}}class Tn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"DualContentModelElementSupport"}init(){this.editor.plugins.get(On).on("register",((t,r)=>{const o=r,n=this.editor,i=n.model.schema,s=n.conversion;if(!o.paragraphLikeModel)return;if(i.isRegistered(o.model)||i.isRegistered(o.paragraphLikeModel))return;const l={model:o.paragraphLikeModel,view:o.view};i.register(o.model,o.modelSchema),i.register(l.model,{inheritAllFrom:"$block"}),s.for("upcast").elementToElement({view:o.view,model:(t,{writer:e})=>this._hasBlockContent(t)?e.createElement(o.model):e.createElement(l.model),converterPriority:e.priorities.low+.5}),s.for("downcast").elementToElement({view:o.view,model:o.model}),this._addAttributeConversion(o),s.for("downcast").elementToElement({view:l.view,model:l.model}),this._addAttributeConversion(l),t.stop()}))}_hasBlockContent(t){const e=this.editor.editing.view,r=e.domConverter.blockElements;for(const o of e.createRangeIn(t).getItems())if(o.is("element")&&r.includes(o.name))return!0;return!1}_addAttributeConversion(t){const e=this.editor,r=e.conversion,o=e.plugins.get(On);e.model.schema.extend(t.model,{allowAttributes:Po(t.view)}),r.for("upcast").add(Mo(t,o)),r.for("downcast").add(Lo(t))}}var Dn=r(331);class Bn extends t.Plugin{static get requires(){return[hn,Dn.Enter]}static get pluginName(){return"HeadingElementSupport"}init(){const t=this.editor;if(!t.plugins.has("HeadingEditing"))return;const e=t.config.get("heading.options");this.registerHeadingElements(t,e)}registerHeadingElements(t,e){const r=t.plugins.get(hn),o=[];for(const t of e)"model"in t&&"view"in t&&(r.registerBlockElement({view:t.view,model:t.model}),o.push(t.model));r.extendBlockElement({model:"htmlHgroup",modelSchema:{allowChildren:o}})}}function Rn(t,e,r){const o=t.createRangeOn(e);for(const{item:t}of o.getWalker())if(t.is("element",r))return t}class Nn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"ImageElementSupport"}init(){const t=this.editor;if(!t.plugins.has("ImageInlineEditing")&&!t.plugins.has("ImageBlockEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On);o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("image"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on("register:img",((n,i)=>{"imageBlock"!==i.model&&"imageInline"!==i.model||(e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["htmlImgAttributes","htmlFigureAttributes","htmlLinkAttributes"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["htmlA","htmlImgAttributes"]}),r.for("upcast").add(function(t){return e=>{e.on("element:img",((e,r,o)=>{if(!r.modelRange)return;const n=r.viewItem,i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlImgAttributes",i,r.modelRange)}),{priority:"low"})}}(o)),r.for("downcast").add((t=>{function e(e){t.on(`attribute:${e}:imageInline`,((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item);xo(r.writer,o,n,i)}),{priority:"low"})}function r(e,r){t.on(`attribute:${r}:imageBlock`,((t,r,o)=>{if(!o.consumable.test(r.item,t.name))return;const{attributeOldValue:n,attributeNewValue:i}=r,s=o.mapper.toViewElement(r.item),l=Rn(o.writer,s,e);l&&(xo(o.writer,n,i,l),o.consumable.consume(r.item,t.name))}),{priority:"low"}),"a"===e&&t.on("attribute:linkHref:imageBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,"attribute:htmlLinkAttributes:imageBlock"))return;const o=r.mapper.toViewElement(e.item),n=Rn(r.writer,o,"a");Fo(r.writer,e.item.getAttribute("htmlLinkAttributes"),n)}),{priority:"low"})}e("htmlImgAttributes"),r("img","htmlImgAttributes"),r("figure","htmlFigureAttributes"),r("a","htmlLinkAttributes")})),t.plugins.has("LinkImage")&&r.for("upcast").add(function(t,e){const r=e.plugins.get("ImageUtils");return e=>{e.on("element:a",((e,o,n)=>{const i=o.viewItem;if(!r.findViewImgElement(i))return;const s=o.modelCursor.parent;if(!s.is("element","imageBlock"))return;const l=t.processViewAttributes(i,n);l&&n.writer.setAttribute("htmlLinkAttributes",l,s)}),{priority:"low"})}}(o,t)),n.stop())}))}}class Mn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"MediaEmbedElementSupport"}init(){const t=this.editor;if(!t.plugins.has("MediaEmbed")||t.config.get("mediaEmbed.previewsInData"))return;const e=t.model.schema,r=t.conversion,o=this.editor.plugins.get(On),n=this.editor.plugins.get(hn),i=t.config.get("mediaEmbed.elementName");n.registerBlockElement({model:"media",view:i}),o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("media"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on(`register:${i}`,((t,n)=>{"media"===n.model&&(e.extend("media",{allowAttributes:[Po(i),"htmlFigureAttributes"]}),r.for("upcast").add(function(t,e){const r=(r,o,n)=>{function i(e,r){const i=t.processViewAttributes(e,n);i&&n.writer.setAttribute(r,i,o.modelRange)}i(o.viewItem,Po(e))};return t=>{t.on(`element:${e}`,r,{priority:"low"})}}(o,i)),r.for("dataDowncast").add(function(t){return e=>{function r(t,r){e.on(`attribute:${r}:media`,((e,r,o)=>{if(!o.consumable.consume(r.item,e.name))return;const{attributeOldValue:n,attributeNewValue:i}=r,s=o.mapper.toViewElement(r.item),l=Rn(o.writer,s,t);xo(o.writer,n,i,l)}))}r(t,Po(t)),r("figure","htmlFigureAttributes")}}(i)),t.stop())}))}}class Ln extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"ScriptElementSupport"}init(){const t=this.editor.plugins.get(On);t.on("register:script",((e,r)=>{const o=this.editor,n=o.model.schema,i=o.conversion;n.register("htmlScript",r.modelSchema),n.extend("htmlScript",{allowAttributes:["htmlScriptAttributes","htmlContent"],isContent:!0}),o.data.registerRawContentMatcher({name:"script"}),i.for("upcast").elementToElement({view:"script",model:$o(r)}),i.for("upcast").add(Mo(r,t)),i.for("downcast").elementToElement({model:"htmlScript",view:(t,{writer:e})=>Do("script",t,e)}),i.for("downcast").add(Lo(r)),e.stop()}))}}class Vn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"TableElementSupport"}init(){const t=this.editor;if(!t.plugins.has("TableEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On),n=t.plugins.get("TableUtils");o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("table"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on("register:table",((i,s)=>{"table"===s.model&&(e.extend("table",{allowAttributes:["htmlTableAttributes","htmlFigureAttributes","htmlTheadAttributes","htmlTbodyAttributes"]}),r.for("upcast").add(function(t){return e=>{e.on("element:table",((e,r,o)=>{if(!r.modelRange)return;const n=r.viewItem;i(n,"htmlTableAttributes");for(const t of n.getChildren())t.is("element","thead")&&i(t,"htmlTheadAttributes"),t.is("element","tbody")&&i(t,"htmlTbodyAttributes");function i(e,n){const i=t.processViewAttributes(e,o);i&&o.writer.setAttribute(n,i,r.modelRange)}}),{priority:"low"})}}(o)),r.for("downcast").add((t=>{function e(e,r){t.on(`attribute:${r}:table`,((t,r,o)=>{if(!o.consumable.test(r.item,t.name))return;const n=o.mapper.toViewElement(r.item),i=Rn(o.writer,n,e);i&&(o.consumable.consume(r.item,t.name),xo(o.writer,r.attributeOldValue,r.attributeNewValue,i))}))}e("table","htmlTableAttributes"),e("figure","htmlFigureAttributes"),e("thead","htmlTheadAttributes"),e("tbody","htmlTbodyAttributes")})),t.model.document.registerPostFixer(function(t,e){return r=>{const o=t.document.differ.getChanges();let n=!1;for(const t of o){if("attribute"!=t.type||"headingRows"!=t.attributeKey)continue;const o=t.range.start.nodeAfter,i=o.getAttribute("htmlTheadAttributes"),s=o.getAttribute("htmlTbodyAttributes");i&&!t.attributeNewValue?(r.removeAttribute("htmlTheadAttributes",o),n=!0):s&&t.attributeNewValue==e.getRows(o)&&(r.removeAttribute("htmlTbodyAttributes",o),n=!0)}return n}}(t.model,n)),i.stop())}))}}class Hn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"StyleElementSupport"}init(){const t=this.editor.plugins.get(On);t.on("register:style",((e,r)=>{const o=this.editor,n=o.model.schema,i=o.conversion;n.register("htmlStyle",r.modelSchema),n.extend("htmlStyle",{allowAttributes:["htmlStyleAttributes","htmlContent"],isContent:!0}),o.data.registerRawContentMatcher({name:"style"}),i.for("upcast").elementToElement({view:"style",model:$o(r)}),i.for("upcast").add(Mo(r,t)),i.for("downcast").elementToElement({model:"htmlStyle",view:(t,{writer:e})=>Do("style",t,e)}),i.for("downcast").add(Lo(r)),e.stop()}))}}const zn=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this};const Un=function(t){return this.__data__.has(t)};function Wn(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new bt;++e<r;)this.add(t[e])}Wn.prototype.add=Wn.prototype.push=zn,Wn.prototype.has=Un;const qn=Wn;const Gn=function(t,e){for(var r=-1,o=null==t?0:t.length;++r<o;)if(e(t[r],r,t))return!0;return!1};const Zn=function(t,e){return t.has(e)};const Kn=function(t,e,r,o,n,i){var s=1&r,l=t.length,a=e.length;if(l!=a&&!(s&&a>l))return!1;var c=i.get(t),u=i.get(e);if(c&&u)return c==e&&u==t;var m=-1,d=!0,f=2&r?new qn:void 0;for(i.set(t,e),i.set(e,t);++m<l;){var h=t[m],b=e[m];if(o)var p=s?o(b,h,m,e,t,i):o(h,b,m,t,e,i);if(void 0!==p){if(p)continue;d=!1;break}if(f){if(!Gn(e,(function(t,e){if(!Zn(f,e)&&(h===t||n(h,t,r,o,i)))return f.push(e)}))){d=!1;break}}else if(h!==b&&!n(h,b,r,o,i)){d=!1;break}}return i.delete(t),i.delete(e),d};const Yn=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,o){r[++e]=[o,t]})),r};const Jn=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r};var Xn=E?E.prototype:void 0,Qn=Xn?Xn.valueOf:void 0;const ti=function(t,e,r,o,n,i,s){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!i(new Ke(t),new Ke(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return l(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var a=Yn;case"[object Set]":var c=1&o;if(a||(a=Jn),t.size!=e.size&&!c)return!1;var u=s.get(t);if(u)return u==e;o|=2,s.set(t,e);var m=Kn(a(t),a(e),o,n,i,s);return s.delete(t),m;case"[object Symbol]":if(Qn)return Qn.call(t)==Qn.call(e)}return!1};var ei=Object.prototype.hasOwnProperty;const ri=function(t,e,r,o,n,i){var s=1&r,l=xe(t),a=l.length;if(a!=xe(e).length&&!s)return!1;for(var c=a;c--;){var u=l[c];if(!(s?u in e:ei.call(e,u)))return!1}var m=i.get(t),d=i.get(e);if(m&&d)return m==e&&d==t;var f=!0;i.set(t,e),i.set(e,t);for(var h=s;++c<a;){var b=t[u=l[c]],p=e[u];if(o)var g=s?o(p,b,u,e,t,i):o(b,p,u,t,e,i);if(!(void 0===g?b===p||n(b,p,r,o,i):g)){f=!1;break}h||(h="constructor"==u)}if(f&&!h){var v=t.constructor,w=e.constructor;v==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof w&&w instanceof w||(f=!1)}return i.delete(t),i.delete(e),f};var oi="[object Arguments]",ni="[object Array]",ii="[object Object]",si=Object.prototype.hasOwnProperty;const li=function(t,e,r,o,n,i){var s=Pt(t),l=Pt(e),a=s?ni:qe(t),c=l?ni:qe(e),u=(a=a==oi?ii:a)==ii,m=(c=c==oi?ii:c)==ii,d=a==c;if(d&&Rt(t)){if(!Rt(e))return!1;s=!0,u=!1}if(d&&!u)return i||(i=new vt),s||Kt(t)?Kn(t,e,r,o,n,i):ti(t,e,a,r,o,n,i);if(!(1&r)){var f=u&&si.call(t,"__wrapped__"),h=m&&si.call(e,"__wrapped__");if(f||h){var b=f?t.value():t,p=h?e.value():e;return i||(i=new vt),n(b,p,r,o,i)}}return!!d&&(i||(i=new vt),ri(t,e,r,o,n,i))};const ai=function t(e,r,o,n,i){return e===r||(null==e||null==r||!kt(e)&&!kt(r)?e!=e&&r!=r:li(e,r,o,n,t,i))};const ci=function(t,e){return ai(t,e)};class ui extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"DocumentListElementSupport"}init(){const t=this.editor;if(!t.plugins.has("DocumentListEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On),n=t.plugins.get("DocumentListEditing"),i=["ul","ol","li"];n.registerDowncastStrategy({scope:"item",attributeName:"htmlLiAttributes",setAttributeOnDowncast:Fo}),n.registerDowncastStrategy({scope:"list",attributeName:"htmlUlAttributes",setAttributeOnDowncast:Fo}),n.registerDowncastStrategy({scope:"list",attributeName:"htmlOlAttributes",setAttributeOnDowncast:Fo}),o.on("register",((t,n)=>{if(!i.includes(n.view))return;if(t.stop(),e.checkAttribute("$block","htmlLiAttributes"))return;const s=i.map((t=>Po(t)));e.extend("$listItem",{allowAttributes:s}),r.for("upcast").add((t=>{t.on("element:ul",mi("htmlUlAttributes",o),{priority:"low"}),t.on("element:ol",mi("htmlOlAttributes",o),{priority:"low"}),t.on("element:li",mi("htmlLiAttributes",o),{priority:"low"})}))})),n.on("postFixer",((t,{listNodes:e,writer:r})=>{for(const{node:o,previousNodeInList:n}of e)if(n){if(n.getAttribute("listType")==o.getAttribute("listType")){const e=di(n.getAttribute("listType")),i=n.getAttribute(e);!ci(o.getAttribute(e),i)&&r.model.schema.checkAttribute(o,e)&&(r.setAttribute(e,i,o),t.return=!0)}if(n.getAttribute("listItemId")==o.getAttribute("listItemId")){const e=n.getAttribute("htmlLiAttributes");!ci(o.getAttribute("htmlLiAttributes"),e)&&r.model.schema.checkAttribute(o,"htmlLiAttributes")&&(r.setAttribute("htmlLiAttributes",e,o),t.return=!0)}}})),n.on("postFixer",((t,{listNodes:e,writer:r})=>{for(const{node:o}of e){const e=o.getAttribute("listType");"numbered"!==e&&o.getAttribute("htmlOlAttributes")&&(r.removeAttribute("htmlOlAttributes",o),t.return=!0),"numbered"===e&&o.getAttribute("htmlUlAttributes")&&(r.removeAttribute("htmlUlAttributes",o),t.return=!0)}}))}afterInit(){const t=this.editor;if(!t.commands.get("indentList"))return;const e=t.commands.get("indentList");this.listenTo(e,"afterExecute",((e,r)=>{t.model.change((e=>{for(const o of r){const r=di(o.getAttribute("listType"));t.model.schema.checkAttribute(o,r)&&e.setAttribute(r,{},o)}}))}))}}function mi(t,e){return(r,o,n)=>{const i=o.viewItem;o.modelRange||Object.assign(o,n.convertChildren(o.viewItem,o.modelCursor));const s=e.processViewAttributes(i,n);for(const e of o.modelRange.getItems({shallow:!0}))e.hasAttribute("listItemId")&&(e.hasAttribute(t)||n.writer.model.schema.checkAttribute(e,t)&&n.writer.setAttribute(t,s||{},e))}}function di(t){return"numbered"===t?"htmlOlAttributes":"htmlUlAttributes"}class fi extends t.Plugin{static get requires(){return[On,hn]}static get pluginName(){return"CustomElementSupport"}init(){const t=this.editor.plugins.get(On),e=this.editor.plugins.get(hn);t.on("register:$customElement",((r,o)=>{r.stop();const i=this.editor,s=i.model.schema,l=i.conversion,a=i.editing.view.domConverter.unsafeElements,c=i.data.htmlProcessor.domConverter.preElements;s.register(o.model,o.modelSchema),s.extend(o.model,{allowAttributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"],isContent:!0}),i.data.htmlProcessor.domConverter.registerRawContentMatcher({name:"template"}),l.for("upcast").elementToElement({view:/.*/,model:(r,s)=>{if("$comment"==r.name)return null;if(!function(t){try{document.createElement(t)}catch(t){return!1}return!0}(r.name))return null;if(e.getDefinitionsForView(r.name).size)return null;a.includes(r.name)||a.push(r.name),c.includes(r.name)||c.push(r.name);const l=s.writer.createElement(o.model,{htmlElementName:r.name}),u=t.processViewAttributes(r,s);let m;if(u&&s.writer.setAttribute("htmlCustomElementAttributes",u,l),r.is("element","template")&&r.getCustomProperty("$rawContent"))m=r.getCustomProperty("$rawContent");else{const t=new n.UpcastWriter(r.document).createDocumentFragment(r),e=i.data.htmlProcessor.domConverter.viewToDom(t),o=e.firstChild;for(;o.firstChild;)e.appendChild(o.firstChild);o.remove(),m=i.data.htmlProcessor.htmlWriter.getHtml(e)}s.writer.setAttribute("htmlContent",m,l);for(const{item:t}of i.editing.view.createRangeIn(r))s.consumable.consume(t,{name:!0});return l},converterPriority:"low"}),l.for("editingDowncast").elementToElement({model:{name:o.model,attributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"]},view:(t,{writer:e})=>{const r=t.getAttribute("htmlElementName"),o=e.createRawElement(r);return t.hasAttribute("htmlCustomElementAttributes")&&Fo(e,t.getAttribute("htmlCustomElementAttributes"),o),o}}),l.for("dataDowncast").elementToElement({model:{name:o.model,attributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"]},view:(t,{writer:e})=>{const r=t.getAttribute("htmlElementName"),o=t.getAttribute("htmlContent"),n=e.createRawElement(r,null,((t,e)=>{e.setContentOf(t,o)}));return t.hasAttribute("htmlCustomElementAttributes")&&Fo(e,t.getAttribute("htmlCustomElementAttributes"),n),n}})}))}}class hi extends t.Plugin{static get pluginName(){return"GeneralHtmlSupport"}static get requires(){return[On,$n,Tn,Bn,Nn,Mn,Ln,Vn,Hn,ui,fi]}init(){const t=this.editor,e=t.plugins.get(On);e.loadAllowedEmptyElementsConfig(t.config.get("htmlSupport.allowEmpty")||[]),e.loadAllowedConfig(t.config.get("htmlSupport.allow")||[]),e.loadDisallowedConfig(t.config.get("htmlSupport.disallow")||[])}getGhsAttributeNameForElement(t){const e=this.editor.plugins.get("DataSchema"),r=Array.from(e.getDefinitionsForView(t,!1)),o=r.find((t=>t.isInline&&!r[0].isObject));return o?o.model:Po(t)}addModelHtmlClass(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"classes",(t=>{for(const o of(0,e.toArray)(r))t.add(o)}))}))}removeModelHtmlClass(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"classes",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}setModelHtmlAttributes(t,e,r){const o=this.editor.model,n=this.getGhsAttributeNameForElement(t);o.change((t=>{for(const i of bi(o,r,n))Io(t,i,n,"attributes",(t=>{for(const[r,o]of Object.entries(e))t.set(r,o)}))}))}removeModelHtmlAttributes(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"attributes",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}setModelHtmlStyles(t,e,r){const o=this.editor.model,n=this.getGhsAttributeNameForElement(t);o.change((t=>{for(const i of bi(o,r,n))Io(t,i,n,"styles",(t=>{for(const[r,o]of Object.entries(e))t.set(r,o)}))}))}removeModelHtmlStyles(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"styles",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}}function*bi(t,e,r){if(e)if(!(Symbol.iterator in e)&&e.is("documentSelection")&&e.isCollapsed)t.schema.checkAttributeInSelection(e,r)&&(yield e);else for(const o of function(t,e,r){return!(Symbol.iterator in e)&&(e.is("node")||e.is("$text")||e.is("$textProxy"))?t.schema.checkAttribute(e,r)?[t.createRangeOn(e)]:[]:t.schema.getValidRanges(t.createSelection(e).getRanges(),r)}(t,e,r))yield*o.getItems({shallow:!0})}class pi extends t.Plugin{static get pluginName(){return"HtmlComment"}init(){const t=this.editor,r=new Map;t.data.processor.skipComments=!1,t.model.schema.addAttributeCheck(((t,e)=>{if(t.endsWith("$root")&&e.startsWith("$comment"))return!0})),t.conversion.for("upcast").elementToMarker({view:"$comment",model:t=>{const o=`$comment:${(0,e.uid)()}`,n=t.getCustomProperty("$rawContent");return r.set(o,n),o}}),t.conversion.for("dataDowncast").markerToElement({model:"$comment",view:(t,{writer:e})=>{let r;for(const e of this.editor.model.document.getRootNames())if(r=this.editor.model.document.getRoot(e),r.hasAttribute(t.markerName))break;const o=t.markerName,n=r.getAttribute(o),i=e.createUIElement("$comment");return e.setCustomProperty("$rawContent",n,i),i}}),t.model.document.registerPostFixer((e=>{let o=!1;const n=t.model.document.differ.getChangedMarkers().filter((t=>t.name.startsWith("$comment:")));for(const t of n){const{oldRange:n,newRange:i}=t.data;if(!n||!i||n.root!=i.root){if(n){const r=n.root;r.hasAttribute(t.name)&&(e.removeAttribute(t.name,r),o=!0)}if(i){const n=i.root;"$graveyard"==n.rootName?(e.removeMarker(t.name),o=!0):n.hasAttribute(t.name)||(e.setAttribute(t.name,r.get(t.name)||"",n),o=!0)}}}return o})),t.data.on("set",(()=>{for(const e of t.model.markers.getMarkersGroup("$comment"))this.removeHtmlComment(e.name)}),{priority:"high"}),t.model.on("deleteContent",((e,[r])=>{for(const e of r.getRanges()){const r=t.model.schema.getLimitElement(e),o=t.model.createPositionAt(r,0),n=t.model.createPositionAt(r,"end");let i;i=o.isTouching(e.start)&&n.isTouching(e.end)?this.getHtmlCommentsInRange(t.model.createRange(o,n)):this.getHtmlCommentsInRange(e,{skipBoundaries:!0});for(const t of i)this.removeHtmlComment(t)}}),{priority:"high"})}createHtmlComment(t,r){const o=(0,e.uid)(),n=this.editor.model,i=n.document.getRoot(t.root.rootName),s=`$comment:${o}`;return n.change((e=>{const o=e.createRange(t);return e.addMarker(s,{usingOperation:!0,affectsData:!0,range:o}),e.setAttribute(s,r,i),s}))}removeHtmlComment(t){const e=this.editor,r=e.model.markers.get(t);return!!r&&(e.model.change((t=>{t.removeMarker(r)})),!0)}getHtmlCommentData(t){const e=this.editor.model.markers.get(t);if(!e)return null;let r="";for(const e of this.editor.model.document.getRoots())if(e.hasAttribute(t)){r=e.getAttribute(t);break}return{content:r,position:e.getStart()}}getHtmlCommentsInRange(t,{skipBoundaries:e=!1}={}){const r=!e;return Array.from(this.editor.model.markers.getMarkersGroup("$comment")).filter((e=>function(t,e){const o=t.getRange().start;return(o.isAfter(e.start)||r&&o.isEqual(e.start))&&(o.isBefore(e.end)||r&&o.isEqual(e.end))}(e,t))).map((t=>t.name))}}class gi extends n.HtmlDataProcessor{toView(t){if(!t.match(/<(?:html|body|head|meta)(?:\s[^>]*)?>/i))return super.toView(t);let e="",r="";t=(t=t.replace(/<!DOCTYPE[^>]*>/i,(t=>(e=t,"")))).replace(/<\?xml\s[^?]*\?>/i,(t=>(r=t,"")));const o=this._toDom(t),i=this.domConverter.domToView(o,{skipComments:this.skipComments}),s=new n.UpcastWriter(i.document);return s.setCustomProperty("$fullPageDocument",o.ownerDocument.documentElement.outerHTML,i),e&&s.setCustomProperty("$fullPageDocType",e,i),r&&s.setCustomProperty("$fullPageXmlDeclaration",r,i),i}toData(t){let e=super.toData(t);const r=t.getCustomProperty("$fullPageDocument"),o=t.getCustomProperty("$fullPageDocType"),n=t.getCustomProperty("$fullPageXmlDeclaration");return r&&(e=r.replace(/<\/body\s*>/,e+"$&"),o&&(e=o+"\n"+e),n&&(e=n+"\n"+e)),e}}class vi extends t.Plugin{static get pluginName(){return"FullPage"}init(){const t=this.editor,e=["$fullPageDocument","$fullPageDocType","$fullPageXmlDeclaration"];t.data.processor=new gi(t.data.viewDocument),t.model.schema.extend("$root",{allowAttributes:e}),t.data.on("toModel",((r,[o])=>{const n=t.model.document.getRoot();t.model.change((t=>{for(const r of e){const e=o.getCustomProperty(r);e&&t.setAttribute(r,e,n)}}))}),{priority:"low"}),t.data.on("toView",((t,[r])=>{if(!r.is("rootElement"))return;const o=r,i=t.return;if(!o.hasAttribute("$fullPageDocument"))return;const s=new n.UpcastWriter(i.document);for(const t of e){const e=o.getAttribute(t);e&&s.setCustomProperty(t,e,i)}}),{priority:"low"}),t.data.on("set",(()=>{const r=t.model.document.getRoot();t.model.change((t=>{for(const o of e)r.hasAttribute(o)&&t.removeAttribute(o,r)}))}),{priority:"high"}),t.data.on("get",((t,e)=>{e[0]||(e[0]={}),e[0].trim=!1}),{priority:"high"})}}})(),(window.CKEditor5=window.CKEditor5||{}).htmlSupport=o})();