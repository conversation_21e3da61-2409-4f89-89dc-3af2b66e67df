!function(e){const i=e.tr=e.tr||{};i.dictionary=Object.assign(i.dictionary||{},{"%0 of %1":"%0/%1",Accept:"Kabul et",Aquamarine:"<PERSON> Yeşili",Black:"<PERSON>yah",Blue:"<PERSON><PERSON>",Cancel:"<PERSON>ptal","Cannot upload file:":"Dosya yüklenemedi:",Clear:"Temizle","Click to edit block":"Bloğu düzenlemek için tıkla","Dim grey":"Koyu Gri","Drag to move":"Taşımak için sürükle","Dropdown toolbar":"Açılır araç çubuğu","Edit block":"Bloğu Düzenle","Editor block content toolbar":"Düzenleyici engelleme içerik araç çubuğu","Editor contextual toolbar":"Düzenleyici içeriksel araç çubuğu","Editor editing area: %0":"Editör düzenleme alanı: %0","Editor toolbar":"Düzenleme araç çubuğu",Green:"Ye<PERSON><PERSON>",Grey:"<PERSON><PERSON>",HEX:"ONALTILIK","Insert image with file manager":"Dosya yöneticisiyle görüntü ekleyin","Insert paragraph after block":"Bloktan sonra paragraf ekle","Insert paragraph before block":"Bloktan önce paragraf ekle","Insert with file manager":"Dosya yöneticisiyle ekle","Light blue":"Açık Mavi","Light green":"Açık Yeşil","Light grey":"Açık Gri",Next:"Sonraki","No results found":"Sonuç bulunamadı","No searchable items":"Aranabilir öge yok",Orange:"Turuncu","Press Enter to type after or press Shift + Enter to type before the widget":"Görsel bileşenden sonra yazmak için Enter'a basın ya da görsel bileşenden önce yazmak için Shift + Enter'a basın",Previous:"Önceki",Purple:"Mor",Red:"Kırmızı",Redo:"Tekrar yap","Remove color":"Rengi Sil","Replace image with file manager":"Resmi dosya yöneticisiyle değiştir","Replace with file manager":"Dosya yöneticisiyle değiştirin","Restore default":"Varsayılanı geri yükle","Rich Text Editor":"Zengin İçerik Editörü","Rich Text Editor. Editing area: %0":"Zengin Metin Editörü.Düzenleme alanı: %0",Save:"Kaydet","Select all":"Hepsini seç","Show more items":"Daha fazla öğe göster",Turquoise:"Turkuaz",Undo:"Geri al","Upload in progress":"Yükleme işlemi devam ediyor",White:"Beyaz","Widget toolbar":"Bileşen araç çubuğu",Yellow:"Sarı"}),i.getPluralForm=function(e){return e>1}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));