!function(e){const i=e["en-gb"]=e["en-gb"]||{};i.dictionary=Object.assign(i.dictionary||{},{"Break text":"","Caption for image: %0":"","Caption for the image":"","Centered image":"Centred image","Change image text alternative":"Change image text alternative","Enter image caption":"Enter image caption","Full size image":"Full size image","Image resize list":"","Image toolbar":"","image widget":"Image widget","In line":"",Insert:"","Insert image":"Insert image","Insert image via URL":"","Left aligned image":"Left aligned image",Original:"","Replace from computer":"","Replace image":"","Replace image from computer":"","Resize image":"","Resize image to %0":"","Resize image to the original size":"","Right aligned image":"Right aligned image","Side image":"Side image","Text alternative":"Text alternative",Update:"","Update image URL":"","Upload failed":"Upload failed","Upload from computer":"","Upload image from computer":"","Wrap text":""})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));