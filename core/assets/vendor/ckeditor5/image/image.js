!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Break text":"Break text","Caption for image: %0":"Caption for image: %0","Caption for the image":"Caption for the image","Centered image":"Centered image","Change image text alternative":"Change image text alternative","Enter image caption":"Enter image caption","Full size image":"Full size image","Image resize list":"Image resize list","Image toolbar":"Image toolbar","image widget":"image widget","In line":"In line",Insert:"Insert","Insert image":"Insert image","Insert image via URL":"Insert image via URL","Left aligned image":"Left aligned image",Original:"Original","Replace from computer":"Replace from computer","Replace image":"Replace image","Replace image from computer":"Replace image from computer","Resize image":"Resize image","Resize image to %0":"Resize image to %0","Resize image to the original size":"Resize image to the original size","Right aligned image":"Right aligned image","Side image":"Side image","Text alternative":"Text alternative",Update:"Update","Update image URL":"Update image URL","Upload failed":"Upload failed","Upload from computer":"Upload from computer","Upload image from computer":"Upload image from computer","Wrap text":"Wrap text"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={487:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const a=o},417:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck-content .image{clear:both;display:table;margin:.9em auto;min-width:50px;text-align:center}.ck-content .image img{display:block;height:auto;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{align-items:flex-start;display:inline-flex;max-width:100%}.ck-content .image-inline picture{display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{flex-grow:1;flex-shrink:1;max-width:100%}.ck.ck-editor__editable .image>figcaption.ck-placeholder:before{overflow:hidden;padding-left:inherit;padding-right:inherit;text-overflow:ellipsis;white-space:nowrap}.ck.ck-editor__editable .image{z-index:1}.ck.ck-editor__editable .image.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline{z-index:1}.ck.ck-editor__editable .image-inline.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline.ck-widget_selected ::selection{display:none}.ck.ck-editor__editable .image-inline img{height:auto}.ck.ck-editor__editable td .image-inline img,.ck.ck-editor__editable th .image-inline img{max-width:none}",""]);const a=o},86:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,":root{--ck-color-image-caption-background:#f7f7f7;--ck-color-image-caption-text:#333;--ck-color-image-caption-highligted-background:#fd0}.ck-content .image>figcaption{background-color:var(--ck-color-image-caption-background);caption-side:bottom;color:var(--ck-color-image-caption-text);display:table-caption;font-size:.75em;outline-offset:-1px;padding:.6em;word-break:break-word}.ck.ck-editor__editable .image>figcaption.image__caption_highlighted{animation:ck-image-caption-highlight .6s ease-out}@keyframes ck-image-caption-highlight{0%{background-color:var(--ck-color-image-caption-highligted-background)}to{background-color:var(--ck-color-image-caption-background)}}",""]);const a=o},554:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-image-insert-url .ck-image-insert-url__action-row{display:grid;grid-template-columns:repeat(2,1fr)}:root{--ck-image-insert-insert-by-url-width:250px}.ck.ck-image-insert-url{--ck-input-width:100%}.ck.ck-image-insert-url .ck-image-insert-url__action-row{grid-column-gap:var(--ck-spacing-large);margin-top:var(--ck-spacing-large)}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-cancel,.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-save{justify-content:center;min-width:auto}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button .ck-button__label{color:var(--ck-color-text)}.ck.ck-image-insert-form>.ck.ck-button{display:block;padding:var(--ck-list-button-padding);width:100%}[dir=ltr] .ck.ck-image-insert-form>.ck.ck-button{text-align:left}[dir=rtl] .ck.ck-image-insert-form>.ck.ck-button{text-align:right}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:first-child){border-top:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:last-child){border-bottom:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-collapsible,.ck.ck-image-insert-form>.ck.ck-image-insert-url{min-width:var(--ck-image-insert-insert-by-url-width)}.ck.ck-image-insert-form>.ck.ck-image-insert-url{padding:var(--ck-spacing-large)}.ck.ck-image-insert-form:focus{outline:none}",""]);const a=o},390:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-editor__editable img.image_placeholder{background-size:100% 100%}",""]);const a=o},968:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck-content img.image_resized{height:auto}.ck-content .image.image_resized{box-sizing:border-box;display:block;max-width:100%}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck.ck-editor__editable td .image-inline.image_resized img,.ck.ck-editor__editable th .image-inline.image_resized img{max-width:100%}[dir=ltr] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-right:var(--ck-spacing-standard)}[dir=rtl] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-left:var(--ck-spacing-standard)}.ck.ck-dropdown .ck-button.ck-resize-image-button .ck-button__label{width:4em}",""]);const a=o},151:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,":root{--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing)/2)}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-left:auto;margin-right:0}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-bottom:var(--ck-inline-image-style-spacing);margin-top:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-background)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after{display:none}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-hover-background)}",""]);const a=o},651:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-image-upload-complete-icon{border-radius:50%;display:block;position:absolute;right:min(var(--ck-spacing-medium),6%);top:min(var(--ck-spacing-medium),6%);z-index:1}.ck-image-upload-complete-icon:after{content:"";position:absolute}:root{--ck-color-image-upload-icon:#fff;--ck-color-image-upload-icon-background:#008a00;--ck-image-upload-icon-size:20;--ck-image-upload-icon-width:2px;--ck-image-upload-icon-is-visible:clamp(0px,100% - 50px,1px)}.ck-image-upload-complete-icon{animation-delay:0ms,3s;animation-duration:.5s,.5s;animation-fill-mode:forwards,forwards;animation-name:ck-upload-complete-icon-show,ck-upload-complete-icon-hide;background:var(--ck-color-image-upload-icon-background);font-size:calc(1px*var(--ck-image-upload-icon-size));height:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size));opacity:0;overflow:hidden;width:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size))}.ck-image-upload-complete-icon:after{animation-delay:.5s;animation-duration:.5s;animation-fill-mode:forwards;animation-name:ck-upload-complete-icon-check;border-right:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);border-top:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);box-sizing:border-box;height:0;left:25%;opacity:0;top:50%;transform:scaleX(-1) rotate(135deg);transform-origin:left top;width:0}@keyframes ck-upload-complete-icon-show{0%{opacity:0}to{opacity:1}}@keyframes ck-upload-complete-icon-hide{0%{opacity:1}to{opacity:0}}@keyframes ck-upload-complete-icon-check{0%{height:0;opacity:1;width:0}33%{height:0;width:.3em}to{height:.45em;opacity:1;width:.3em}}',""]);const a=o},582:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck .ck-upload-placeholder-loader{align-items:center;display:flex;justify-content:center;left:0;position:absolute;top:0}.ck .ck-upload-placeholder-loader:before{content:"";position:relative}:root{--ck-color-upload-placeholder-loader:#b3b3b3;--ck-upload-placeholder-loader-size:32px;--ck-upload-placeholder-image-aspect-ratio:2.8}.ck .ck-image-upload-placeholder{margin:0;width:100%}.ck .ck-image-upload-placeholder.image-inline{width:calc(var(--ck-upload-placeholder-loader-size)*2*var(--ck-upload-placeholder-image-aspect-ratio))}.ck .ck-image-upload-placeholder img{aspect-ratio:var(--ck-upload-placeholder-image-aspect-ratio)}.ck .ck-upload-placeholder-loader{height:100%;width:100%}.ck .ck-upload-placeholder-loader:before{animation:ck-upload-placeholder-loader 1s linear infinite;border-radius:50%;border-right:2px solid transparent;border-top:3px solid var(--ck-color-upload-placeholder-loader);height:var(--ck-upload-placeholder-loader-size);width:var(--ck-upload-placeholder-loader-size)}@keyframes ck-upload-placeholder-loader{to{transform:rotate(1turn)}}',""]);const a=o},283:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-editor__editable .image,.ck.ck-editor__editable .image-inline{position:relative}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{left:0;position:absolute;top:0}.ck.ck-editor__editable .image-inline.ck-appear,.ck.ck-editor__editable .image.ck-appear{animation:fadeIn .7s}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{background:var(--ck-color-upload-bar-background);height:2px;transition:width .1s;width:0}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}",""]);const a=o},915:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-text-alternative-form{display:flex;flex-direction:row;flex-wrap:nowrap}.ck.ck-text-alternative-form .ck-labeled-field-view{display:inline-block}.ck.ck-text-alternative-form .ck-label{display:none}@media screen and (max-width:600px){.ck.ck-text-alternative-form{flex-wrap:wrap}.ck.ck-text-alternative-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-text-alternative-form .ck-button{flex-basis:50%}}",""]);const a=o},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i=e(t);return t[2]?"@media ".concat(t[2]," {").concat(i,"}"):i})).join("")},t.i=function(e,i,n){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(n)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(o[s]=!0)}for(var r=0;r<e.length;r++){var l=[].concat(e[r]);n&&o[l[0]]||(i&&(l[2]?l[2]="".concat(i," and ").concat(l[2]):l[2]=i),t.push(l))}},t}},62:(e,t,i)=>{"use strict";var n,o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},a=function(){var e={};return function(t){if(void 0===e[t]){var i=document.querySelector(t);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}e[t]=i}return e[t]}}(),s=[];function r(e){for(var t=-1,i=0;i<s.length;i++)if(s[i].identifier===e){t=i;break}return t}function l(e,t){for(var i={},n=[],o=0;o<e.length;o++){var a=e[o],l=t.base?a[0]+t.base:a[0],c=i[l]||0,g="".concat(l," ").concat(c);i[l]=c+1;var m=r(g),d={css:a[1],media:a[2],sourceMap:a[3]};-1!==m?(s[m].references++,s[m].updater(d)):s.push({identifier:g,updater:f(d,t),references:1}),n.push(g)}return n}function c(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var o=i.nc;o&&(n.nonce=o)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var s=a(e.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(t)}return t}var g,m=(g=[],function(e,t){return g[e]=t,g.filter(Boolean).join("\n")});function d(e,t,i,n){var o=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=m(t,o);else{var a=document.createTextNode(o),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(a,s[t]):e.appendChild(a)}}function u(e,t,i){var n=i.css,o=i.media,a=i.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),a&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var p=null,h=0;function f(e,t){var i,n,o;if(t.singleton){var a=h++;i=p||(p=c(t)),n=d.bind(null,i,a,!1),o=d.bind(null,i,a,!0)}else i=c(t),n=u.bind(null,i,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(i)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var i=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<i.length;n++){var o=r(i[n]);s[o].references--}for(var a=l(e,t),c=0;c<i.length;c++){var g=r(i[c]);0===s[g].references&&(s[g].updater(),s.splice(g,1))}i=a}}}},945:(e,t,i)=>{e.exports=i(79)("./src/clipboard.js")},704:(e,t,i)=>{e.exports=i(79)("./src/core.js")},492:(e,t,i)=>{e.exports=i(79)("./src/engine.js")},181:(e,t,i)=>{e.exports=i(79)("./src/typing.js")},273:(e,t,i)=>{e.exports=i(79)("./src/ui.js")},254:(e,t,i)=>{e.exports=i(79)("./src/undo.js")},448:(e,t,i)=>{e.exports=i(79)("./src/upload.js")},209:(e,t,i)=>{e.exports=i(79)("./src/utils.js")},995:(e,t,i)=>{e.exports=i(79)("./src/widget.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={id:n,exports:{}};return e[n](a,a.exports,i),a.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{AutoImage:()=>k,Image:()=>ee,ImageBlock:()=>Q,ImageBlockEditing:()=>M,ImageCaption:()=>re,ImageCaptionEditing:()=>ne,ImageCaptionUI:()=>oe,ImageCaptionUtils:()=>te,ImageEditing:()=>O,ImageInline:()=>Y,ImageInsert:()=>Ue,ImageInsertUI:()=>K,ImageInsertViaUrl:()=>Ve,ImageResize:()=>We,ImageResizeButtons:()=>Ne,ImageResizeEditing:()=>Re,ImageResizeHandles:()=>Fe,ImageSizeAttributes:()=>L,ImageStyle:()=>dt,ImageStyleEditing:()=>nt,ImageStyleUI:()=>lt,ImageTextAlternative:()=>V,ImageTextAlternativeEditing:()=>w,ImageTextAlternativeUI:()=>B,ImageToolbar:()=>ut,ImageUpload:()=>Ae,ImageUploadEditing:()=>xe,ImageUploadProgress:()=>Ie,ImageUploadUI:()=>de,ImageUtils:()=>h,PictureEditing:()=>pt});var e=i(704),t=i(945),o=i(492),a=i(254),s=i(181),r=i(209),l=i(995);function c(e){return e.createContainerElement("figure",{class:"image"},[e.createEmptyElement("img"),e.createSlot("children")])}function g(e,t){const i=e.plugins.get("ImageUtils"),n=e.plugins.has("ImageInlineEditing")&&e.plugins.has("ImageBlockEditing");return e=>{if(!i.isInlineImageView(e))return null;if(!n)return o(e);return("block"==e.getStyle("display")||e.findAncestor(i.isBlockImageView)?"imageBlock":"imageInline")!==t?null:o(e)};function o(e){const t={name:!0};return e.hasAttribute("src")&&(t.attributes=["src"]),t}}function m(e,t){const i=(0,r.first)(t.getSelectedBlocks());return!i||e.isObject(i)||i.isEmpty&&"listItem"!=i.name?"imageBlock":"imageInline"}function d(e){return e&&e.endsWith("px")?parseInt(e):null}function u(e){const t=d(e.getStyle("width")),i=d(e.getStyle("height"));return!(!t||!i)}const p=/^(image|image-inline)$/;class h extends e.Plugin{constructor(){super(...arguments),this._domEmitter=new((0,r.DomEmitterMixin)())}static get pluginName(){return"ImageUtils"}isImage(e){return this.isInlineImage(e)||this.isBlockImage(e)}isInlineImageView(e){return!!e&&e.is("element","img")}isBlockImageView(e){return!!e&&e.is("element","figure")&&e.hasClass("image")}insertImage(e={},t=null,i=null,n={}){const o=this.editor,a=o.model,s=a.document.selection,r=f(o,t||s,i);e={...Object.fromEntries(s.getAttributes()),...e};for(const t in e)a.schema.checkAttribute(r,t)||delete e[t];return a.change((i=>{const{setImageSizes:o=!0}=n,s=i.createElement(r,e);return a.insertObject(s,t,null,{setSelection:"on",findOptimalPosition:t||"imageInline"==r?void 0:"auto"}),s.parent?(o&&this.setImageNaturalSizeAttributes(s),s):null}))}setImageNaturalSizeAttributes(e){const t=e.getAttribute("src");t&&(e.getAttribute("width")||e.getAttribute("height")||this.editor.model.change((i=>{const n=new r.global.window.Image;this._domEmitter.listenTo(n,"load",(()=>{e.getAttribute("width")||e.getAttribute("height")||this.editor.model.enqueueChange(i.batch,(t=>{t.setAttribute("width",n.naturalWidth,e),t.setAttribute("height",n.naturalHeight,e)})),this._domEmitter.stopListening(n,"load")})),n.src=t})))}getClosestSelectedImageWidget(e){const t=e.getFirstPosition();if(!t)return null;const i=e.getSelectedElement();if(i&&this.isImageWidget(i))return i;let n=t.parent;for(;n;){if(n.is("element")&&this.isImageWidget(n))return n;n=n.parent}return null}getClosestSelectedImageElement(e){const t=e.getSelectedElement();return this.isImage(t)?t:e.getFirstPosition().findAncestor("imageBlock")}getImageWidgetFromImageView(e){return e.findAncestor({classes:p})}isImageAllowed(){const e=this.editor.model.document.selection;return function(e,t){const i=f(e,t,null);if("imageBlock"==i){const i=function(e,t){const i=(0,l.findOptimalInsertionRange)(e,t),n=i.start.parent;if(n.isEmpty&&!n.is("element","$root"))return n.parent;return n}(t,e.model);if(e.model.schema.checkChild(i,"imageBlock"))return!0}else if(e.model.schema.checkChild(t.focus,"imageInline"))return!0;return!1}(this.editor,e)&&function(e){return[...e.focus.getAncestors()].every((e=>!e.is("element","imageBlock")))}(e)}toImageWidget(e,t,i){t.setCustomProperty("image",!0,e);return(0,l.toWidget)(e,t,{label:()=>{const t=this.findViewImgElement(e).getAttribute("alt");return t?`${t} ${i}`:i}})}isImageWidget(e){return!!e.getCustomProperty("image")&&(0,l.isWidget)(e)}isBlockImage(e){return!!e&&e.is("element","imageBlock")}isInlineImage(e){return!!e&&e.is("element","imageInline")}findViewImgElement(e){if(this.isInlineImageView(e))return e;const t=this.editor.editing.view;for(const{item:i}of t.createRangeIn(e))if(this.isInlineImageView(i))return i}destroy(){return this._domEmitter.stopListening(),super.destroy()}}function f(e,t,i){const n=e.model.schema,o=e.config.get("image.insert.type");return e.plugins.has("ImageBlockEditing")?e.plugins.has("ImageInlineEditing")?i||("inline"===o?"imageInline":"auto"!==o?"imageBlock":t.is("selection")?m(n,t):n.checkChild(t,"imageInline")?"imageInline":"imageBlock"):"imageBlock":"imageInline"}const b=new RegExp(String(/^(http(s)?:\/\/)?[\w-]+\.[\w.~:/[\]@!$&'()*+,;=%-]+/.source+/\.(jpg|jpeg|png|gif|ico|webp|JPG|JPEG|PNG|GIF|ICO|WEBP)/.source+/(\?[\w.~:/[\]@!$&'()*+,;=%-]*)?/.source+/(#[\w.~:/[\]@!$&'()*+,;=%-]*)?$/.source));class k extends e.Plugin{static get requires(){return[t.Clipboard,h,a.Undo,s.Delete]}static get pluginName(){return"AutoImage"}constructor(e){super(e),this._timeoutId=null,this._positionToInsert=null}init(){const e=this.editor,t=e.model.document,i=e.plugins.get("ClipboardPipeline");this.listenTo(i,"inputTransformation",(()=>{const e=t.selection.getFirstRange(),i=o.LivePosition.fromPosition(e.start);i.stickiness="toPrevious";const n=o.LivePosition.fromPosition(e.end);n.stickiness="toNext",t.once("change:data",(()=>{this._embedImageBetweenPositions(i,n),i.detach(),n.detach()}),{priority:"high"})})),e.commands.get("undo").on("execute",(()=>{this._timeoutId&&(r.global.window.clearTimeout(this._timeoutId),this._positionToInsert.detach(),this._timeoutId=null,this._positionToInsert=null)}),{priority:"high"})}_embedImageBetweenPositions(e,t){const i=this.editor,n=new o.LiveRange(e,t),a=n.getWalker({ignoreElementEnd:!0}),s=Object.fromEntries(i.model.document.selection.getAttributes()),r=this.editor.plugins.get("ImageUtils");let l="";for(const e of a)e.item.is("$textProxy")&&(l+=e.item.data);l=l.trim(),l.match(b)?(this._positionToInsert=o.LivePosition.fromPosition(e),this._timeoutId=setTimeout((()=>{if(!i.commands.get("insertImage").isEnabled)return void n.detach();i.model.change((e=>{let t;this._timeoutId=null,e.remove(n),n.detach(),"$graveyard"!==this._positionToInsert.root.rootName&&(t=this._positionToInsert.toPosition()),r.insertImage({...s,src:l},t),this._positionToInsert.detach(),this._positionToInsert=null}));i.plugins.get("Delete").requestUndoOnBackspace()}),100)):n.detach()}}class I extends e.Command{refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled&&e.hasAttribute("alt")?this.value=e.getAttribute("alt"):this.value=!1}execute(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n=t.model,o=i.getClosestSelectedImageElement(n.document.selection);n.change((t=>{t.setAttribute("alt",e.newValue,o)}))}}class w extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageTextAlternativeEditing"}init(){this.editor.commands.add("imageTextAlternative",new I(this.editor))}}var v=i(273),y=i(62),_=i.n(y),C=i(915),E={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(C.Z,E);C.Z.locals;var x=i(487),S={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(x.Z,S);x.Z.locals;class A extends v.View{constructor(t){super(t);const i=this.locale.t;this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this.labeledInput=this._createLabeledInputView(),this.saveButtonView=this._createButton(i("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.cancelButtonView=this._createButton(i("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._focusables=new v.ViewCollection,this._focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-text-alternative-form","ck-responsive-form"],tabindex:"-1"},children:[this.labeledInput,this.saveButtonView,this.cancelButtonView]})}render(){super.render(),this.keystrokes.listenTo(this.element),(0,v.submitHandler)({view:this}),[this.labeledInput,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)}))}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createButton(e,t,i,n){const o=new v.ButtonView(this.locale);return o.set({label:e,icon:t,tooltip:!0}),o.extendTemplate({attributes:{class:i}}),n&&o.delegate("execute").to(this,n),o}_createLabeledInputView(){const e=this.locale.t,t=new v.LabeledFieldView(this.locale,v.createLabeledInputText);return t.label=e("Text alternative"),t}}function T(e){const t=e.editing.view,i=v.BalloonPanelView.defaultPositions,n=e.plugins.get("ImageUtils");return{target:t.domConverter.mapViewToDom(n.getClosestSelectedImageWidget(t.document.selection)),positions:[i.northArrowSouth,i.northArrowSouthWest,i.northArrowSouthEast,i.southArrowNorth,i.southArrowNorthWest,i.southArrowNorthEast,i.viewportStickyNorth]}}class B extends e.Plugin{static get requires(){return[v.ContextualBalloon]}static get pluginName(){return"ImageTextAlternativeUI"}init(){this._createButton()}destroy(){super.destroy(),this._form&&this._form.destroy()}_createButton(){const t=this.editor,i=t.t;t.ui.componentFactory.add("imageTextAlternative",(n=>{const o=t.commands.get("imageTextAlternative"),a=new v.ButtonView(n);return a.set({label:i("Change image text alternative"),icon:e.icons.textAlternative,tooltip:!0}),a.bind("isEnabled").to(o,"isEnabled"),a.bind("isOn").to(o,"value",(e=>!!e)),this.listenTo(a,"execute",(()=>{this._showForm()})),a}))}_createForm(){const e=this.editor,t=e.editing.view.document,i=e.plugins.get("ImageUtils");this._balloon=this.editor.plugins.get("ContextualBalloon"),this._form=new((0,v.CssTransitionDisablerMixin)(A))(e.locale),this._form.render(),this.listenTo(this._form,"submit",(()=>{e.execute("imageTextAlternative",{newValue:this._form.labeledInput.fieldView.element.value}),this._hideForm(!0)})),this.listenTo(this._form,"cancel",(()=>{this._hideForm(!0)})),this._form.keystrokes.set("Esc",((e,t)=>{this._hideForm(!0),t()})),this.listenTo(e.ui,"update",(()=>{i.getClosestSelectedImageWidget(t.selection)?this._isVisible&&function(e){const t=e.plugins.get("ContextualBalloon");if(e.plugins.get("ImageUtils").getClosestSelectedImageWidget(e.editing.view.document.selection)){const i=T(e);t.updatePosition(i)}}(e):this._hideForm(!0)})),(0,v.clickOutsideHandler)({emitter:this._form,activator:()=>this._isVisible,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideForm()})}_showForm(){if(this._isVisible)return;this._form||this._createForm();const e=this.editor,t=e.commands.get("imageTextAlternative"),i=this._form.labeledInput;this._form.disableCssTransitions(),this._isInBalloon||this._balloon.add({view:this._form,position:T(e)}),i.fieldView.value=i.fieldView.element.value=t.value||"",this._form.labeledInput.fieldView.select(),this._form.enableCssTransitions()}_hideForm(e=!1){this._isInBalloon&&(this._form.focusTracker.isFocused&&this._form.saveButtonView.focus(),this._balloon.remove(this._form),e&&this.editor.editing.view.focus())}get _isVisible(){return!!this._balloon&&this._balloon.visibleView===this._form}get _isInBalloon(){return!!this._balloon&&this._balloon.hasView(this._form)}}class V extends e.Plugin{static get requires(){return[w,B]}static get pluginName(){return"ImageTextAlternative"}}function U(e,t){const i=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a);null===i.attributeNewValue?(o.removeAttribute("srcset",s),o.removeAttribute("sizes",s)):i.attributeNewValue&&(o.setAttribute("srcset",i.attributeNewValue,s),o.setAttribute("sizes","100vw",s))};return e=>{e.on(`attribute:srcset:${t}`,i)}}function z(e,t,i){const n=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a);o.setAttribute(i.attributeKey,i.attributeNewValue||"",s)};return e=>{e.on(`attribute:${i}:${t}`,n)}}class R extends o.Observer{observe(e){this.listenTo(e,"load",((e,t)=>{const i=t.target;this.checkShouldIgnoreEventFromTarget(i)||"IMG"==i.tagName&&this._fireEvents(t)}),{useCapture:!0})}stopObserving(e){this.stopListening(e)}_fireEvents(e){this.isEnabled&&(this.document.fire("layoutChanged"),this.document.fire("imageLoaded",e))}}class P extends e.Command{constructor(e){super(e);const t=e.config.get("image.insert.type");e.plugins.has("ImageBlockEditing")||"block"===t&&(0,r.logWarning)("image-block-plugin-required"),e.plugins.has("ImageInlineEditing")||"inline"===t&&(0,r.logWarning)("image-inline-plugin-required")}refresh(){const e=this.editor.plugins.get("ImageUtils");this.isEnabled=e.isImageAllowed()}execute(e){const t=(0,r.toArray)(e.source),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((e,t)=>{const a=i.getSelectedElement();if("string"==typeof e&&(e={src:e}),t&&a&&n.isImage(a)){const t=this.editor.model.createPositionAfter(a);n.insertImage({...e,...o},t)}else n.insertImage({...e,...o})}))}}class N extends e.Command{constructor(e){super(e),this.decorate("cleanupImage")}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=this.editor.model.document.selection.getSelectedElement();this.isEnabled=e.isImage(t),this.value=this.isEnabled?t.getAttribute("src"):null}execute(e){const t=this.editor.model.document.selection.getSelectedElement(),i=this.editor.plugins.get("ImageUtils");this.editor.model.change((n=>{n.setAttribute("src",e.source,t),this.cleanupImage(n,t),i.setImageNaturalSizeAttributes(t)}))}cleanupImage(e,t){e.removeAttribute("srcset",t),e.removeAttribute("sizes",t),e.removeAttribute("sources",t),e.removeAttribute("width",t),e.removeAttribute("height",t),e.removeAttribute("alt",t)}}class O extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageEditing"}init(){const e=this.editor,t=e.conversion;e.editing.view.addObserver(R),t.for("upcast").attributeToAttribute({view:{name:"img",key:"alt"},model:"alt"}).attributeToAttribute({view:{name:"img",key:"srcset"},model:"srcset"});const i=new P(e),n=new N(e);e.commands.add("insertImage",i),e.commands.add("replaceImageSource",n),e.commands.add("imageInsert",i)}}class L extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageSizeAttributes"}afterInit(){this._registerSchema(),this._registerConverters("imageBlock"),this._registerConverters("imageInline")}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["width","height"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["width","height"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n="imageBlock"===e?"figure":"img";function o(t,n,o,a){t.on(`attribute:${n}:${e}`,((t,n,s)=>{if(!s.consumable.consume(n.item,t.name))return;const r=s.writer,l=s.mapper.toViewElement(n.item),c=i.findViewImgElement(l);if(null!==n.attributeNewValue?r.setAttribute(o,n.attributeNewValue,c):r.removeAttribute(o,c),n.item.hasAttribute("sources"))return;const g=n.item.hasAttribute("resizedWidth");if("imageInline"===e&&!g&&!a)return;const m=n.item.getAttribute("width"),d=n.item.getAttribute("height");m&&d&&r.setStyle("aspect-ratio",`${m}/${d}`,c)}))}t.conversion.for("upcast").attributeToAttribute({view:{name:n,styles:{width:/.+/}},model:{key:"width",value:e=>u(e)?d(e.getStyle("width")):null}}).attributeToAttribute({view:{name:n,key:"width"},model:"width"}).attributeToAttribute({view:{name:n,styles:{height:/.+/}},model:{key:"height",value:e=>u(e)?d(e.getStyle("height")):null}}).attributeToAttribute({view:{name:n,key:"height"},model:"height"}),t.conversion.for("editingDowncast").add((e=>{o(e,"width","width",!0),o(e,"height","height",!0)})),t.conversion.for("dataDowncast").add((e=>{o(e,"width","width",!1),o(e,"height","height",!1)}))}}class F extends e.Command{constructor(e,t){super(e),this._modelElementName=t}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=e.getClosestSelectedImageElement(this.editor.model.document.selection);"imageBlock"===this._modelElementName?this.isEnabled=e.isInlineImage(t):this.isEnabled=e.isBlockImage(t)}execute(e={}){const t=this.editor,i=this.editor.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection),a=Object.fromEntries(o.getAttributes());return a.src||a.uploadId?i.change((t=>{const{setImageSizes:s=!0}=e,r=Array.from(i.markers).filter((e=>e.getRange().containsItem(o))),l=n.insertImage(a,i.createSelection(o,"on"),this._modelElementName,{setImageSizes:s});if(!l)return null;const c=t.createRangeOn(l);for(const e of r){const i=e.getRange(),n="$graveyard"!=i.root.rootName?i.getJoined(c,!0):c;t.updateMarker(e,{range:n})}return{oldElement:o,newElement:l}})):null}}var D=i(390),j={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(D.Z,j);D.Z.locals;class W extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImagePlaceholder"}afterInit(){this._setupSchema(),this._setupConversion(),this._setupLoadListener()}_setupSchema(){const e=this.editor.model.schema;e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["placeholder"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["placeholder"]})}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("editingDowncast").add((e=>{e.on("attribute:placeholder",((e,t,n)=>{if(!n.consumable.test(t.item,e.name))return;if(!t.item.is("element","imageBlock")&&!t.item.is("element","imageInline"))return;n.consumable.consume(t.item,e.name);const o=n.writer,a=n.mapper.toViewElement(t.item),s=i.findViewImgElement(a);t.attributeNewValue?(o.addClass("image_placeholder",s),o.setStyle("background-image",`url(${t.attributeNewValue})`,s),o.setCustomProperty("editingPipeline:doNotReuseOnce",!0,s)):(o.removeClass("image_placeholder",s),o.removeStyle("background-image",s))}))}))}_setupLoadListener(){const e=this.editor,t=e.model,i=e.editing,n=i.view,o=e.plugins.get("ImageUtils");n.addObserver(R),this.listenTo(n.document,"imageLoaded",((e,a)=>{const s=n.domConverter.mapDomToView(a.target);if(!s)return;const r=o.getImageWidgetFromImageView(s);if(!r)return;const l=i.mapper.toModelElement(r);l&&l.hasAttribute("placeholder")&&t.enqueueChange({isUndoable:!1},(e=>{e.removeAttribute("placeholder",l)}))}))}}class M extends e.Plugin{static get requires(){return[O,L,h,W,t.ClipboardPipeline]}static get pluginName(){return"ImageBlockEditing"}init(){const e=this.editor;e.model.schema.register("imageBlock",{inheritAllFrom:"$blockObject",allowAttributes:["alt","src","srcset"]}),this._setupConversion(),e.plugins.has("ImageInlineEditing")&&(e.commands.add("imageTypeBlock",new F(this.editor,"imageBlock")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:t})=>c(t)}),i.for("editingDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:i})=>n.toImageWidget(c(i),i,t("image widget"))}),i.for("downcast").add(z(n,"imageBlock","src")).add(z(n,"imageBlock","alt")).add(U(n,"imageBlock")),i.for("upcast").elementToElement({view:g(e,"imageBlock"),model:(e,{writer:t})=>t.createElement("imageBlock",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)}).add(function(e){const t=(t,i,n)=>{if(!n.consumable.test(i.viewItem,{name:!0,classes:"image"}))return;const o=e.findViewImgElement(i.viewItem);if(!o||!n.consumable.test(o,{name:!0}))return;n.consumable.consume(i.viewItem,{name:!0,classes:"image"});const a=n.convertItem(o,i.modelCursor),s=(0,r.first)(a.modelRange.getItems());s?(n.convertChildren(i.viewItem,s),n.updateConversionResult(s,i)):n.consumable.revert(i.viewItem,{name:!0,classes:"image"})};return e=>{e.on("element:figure",t)}}(n))}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),a=e.plugins.get("ClipboardPipeline");this.listenTo(a,"inputTransformation",((a,s)=>{const r=Array.from(s.content.getChildren());let l;if(!r.every(n.isInlineImageView))return;l=s.targetRanges?e.editing.mapper.toModelRange(s.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageBlock"===m(t.schema,c)){const e=new o.UpcastWriter(i.document),t=r.map((t=>e.createElement("figure",{class:"image"},t)));s.content=e.createDocumentFragment(t)}})),this.listenTo(a,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageBlock")&&n.setImageNaturalSizeAttributes(e)}))}))}}var q=i(554),$={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(q.Z,$);q.Z.locals;class Z extends v.View{constructor(e,t=[]){super(e),this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this._focusables=new v.ViewCollection,this.children=this.createCollection(),this._focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}});for(const e of t)this.children.add(e),this._focusables.add(e),e instanceof v.CollapsibleView&&this._focusables.addMany(e.children);if(this._focusables.length>1)for(const e of this._focusables)H(e)&&(e.focusCycler.on("forwardCycle",(e=>{this._focusCycler.focusNext(),e.stop()})),e.focusCycler.on("backwardCycle",(e=>{this._focusCycler.focusPrevious(),e.stop()})));this.setTemplate({tag:"form",attributes:{class:["ck","ck-image-insert-form"],tabindex:-1},children:this.children})}render(){super.render(),(0,v.submitHandler)({view:this});for(const e of this._focusables)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element);const e=e=>e.stopPropagation();this.keystrokes.set("arrowright",e),this.keystrokes.set("arrowleft",e),this.keystrokes.set("arrowup",e),this.keystrokes.set("arrowdown",e)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}}function H(e){return"focusCycler"in e}class K extends e.Plugin{static get pluginName(){return"ImageInsertUI"}static get requires(){return[h]}constructor(e){super(e),this._integrations=new Map,e.config.define("image.insert.integrations",["upload","assetManager","url"])}init(){const e=this.editor,t=e.model.document.selection,i=e.plugins.get("ImageUtils");this.set("isImageSelected",!1),this.listenTo(e.model.document,"change",(()=>{this.isImageSelected=i.isImage(t.getSelectedElement())}));const n=e=>this._createToolbarComponent(e);e.ui.componentFactory.add("insertImage",n),e.ui.componentFactory.add("imageInsert",n)}registerIntegration({name:e,observable:t,buttonViewCreator:i,formViewCreator:n,requiresForm:o}){this._integrations.has(e)&&(0,r.logWarning)("image-insert-integration-exists",{name:e}),this._integrations.set(e,{observable:t,buttonViewCreator:i,formViewCreator:n,requiresForm:!!o})}_createToolbarComponent(e){const t=this.editor,i=e.t,n=this._prepareIntegrations();if(!n.length)return null;let o;const a=n[0];if(1==n.length){if(!a.requiresForm)return a.buttonViewCreator(!0);o=a.buttonViewCreator(!0)}else{const t=a.buttonViewCreator(!1);o=new v.SplitButtonView(e,t),o.tooltip=!0,o.bind("label").to(this,"isImageSelected",(e=>i(e?"Replace image":"Insert image")))}const s=this.dropdownView=(0,v.createDropdown)(e,o),r=n.map((({observable:e})=>e));return s.bind("isEnabled").toMany(r,"isEnabled",((...e)=>e.some((e=>e)))),s.once("change:isOpen",(()=>{const e=n.map((({formViewCreator:e})=>e(1==n.length))),i=new Z(t.locale,e);s.panelView.children.add(i)})),s}_prepareIntegrations(){const e=this.editor.config.get("image.insert.integrations"),t=[];if(!e.length)return(0,r.logWarning)("image-insert-integrations-not-specified"),t;for(const i of e)this._integrations.has(i)?t.push(this._integrations.get(i)):["upload","assetManager","url"].includes(i)||(0,r.logWarning)("image-insert-unknown-integration",{item:i});return t.length||(0,r.logWarning)("image-insert-integrations-not-registered"),t}}var G=i(417),J={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(G.Z,J);G.Z.locals;class Q extends e.Plugin{static get requires(){return[M,l.Widget,V,K]}static get pluginName(){return"ImageBlock"}}class X extends e.Plugin{static get requires(){return[O,L,h,W,t.ClipboardPipeline]}static get pluginName(){return"ImageInlineEditing"}init(){const e=this.editor,t=e.model.schema;t.register("imageInline",{inheritAllFrom:"$inlineObject",allowAttributes:["alt","src","srcset"]}),t.addChildCheck(((e,t)=>{if(e.endsWith("caption")&&"imageInline"===t.name)return!1})),this._setupConversion(),e.plugins.has("ImageBlockEditing")&&(e.commands.add("imageTypeInline",new F(this.editor,"imageInline")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToElement({model:"imageInline",view:(e,{writer:t})=>t.createEmptyElement("img")}),i.for("editingDowncast").elementToStructure({model:"imageInline",view:(e,{writer:i})=>n.toImageWidget(function(e){return e.createContainerElement("span",{class:"image-inline"},e.createEmptyElement("img"))}(i),i,t("image widget"))}),i.for("downcast").add(z(n,"imageInline","src")).add(z(n,"imageInline","alt")).add(U(n,"imageInline")),i.for("upcast").elementToElement({view:g(e,"imageInline"),model:(e,{writer:t})=>t.createElement("imageInline",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)})}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),a=e.plugins.get("ClipboardPipeline");this.listenTo(a,"inputTransformation",((a,s)=>{const r=Array.from(s.content.getChildren());let l;if(!r.every(n.isBlockImageView))return;l=s.targetRanges?e.editing.mapper.toModelRange(s.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageInline"===m(t.schema,c)){const e=new o.UpcastWriter(i.document),t=r.map((t=>1===t.childCount?(Array.from(t.getAttributes()).forEach((i=>e.setAttribute(...i,n.findViewImgElement(t)))),t.getChild(0)):t));s.content=e.createDocumentFragment(t)}})),this.listenTo(a,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageInline")&&n.setImageNaturalSizeAttributes(e)}))}))}}class Y extends e.Plugin{static get requires(){return[X,l.Widget,V,K]}static get pluginName(){return"ImageInline"}}class ee extends e.Plugin{static get requires(){return[Q,Y]}static get pluginName(){return"Image"}}class te extends e.Plugin{static get pluginName(){return"ImageCaptionUtils"}static get requires(){return[h]}getCaptionFromImageModelElement(e){for(const t of e.getChildren())if(t&&t.is("element","caption"))return t;return null}getCaptionFromModelSelection(e){const t=this.editor.plugins.get("ImageUtils"),i=e.getFirstPosition().findAncestor("caption");return i&&t.isBlockImage(i.parent)?i:null}matchImageCaptionViewElement(e){const t=this.editor.plugins.get("ImageUtils");return"figcaption"==e.name&&t.isBlockImageView(e.parent)?{name:!0}:null}}class ie extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageCaptionUtils"),i=e.plugins.get("ImageUtils");if(!e.plugins.has(M))return this.isEnabled=!1,void(this.value=!1);const n=e.model.document.selection,o=n.getSelectedElement();if(!o){const e=t.getCaptionFromModelSelection(n);return this.isEnabled=!!e,void(this.value=!!e)}this.isEnabled=i.isImage(o),this.isEnabled?this.value=!!t.getCaptionFromImageModelElement(o):this.value=!1}execute(e={}){const{focusCaptionOnShow:t}=e;this.editor.model.change((e=>{this.value?this._hideImageCaption(e):this._showImageCaption(e,t)}))}_showImageCaption(e,t){const i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageCaptionEditing"),o=this.editor.plugins.get("ImageUtils");let a=i.getSelectedElement();const s=n._getSavedCaption(a);o.isInlineImage(a)&&(this.editor.execute("imageTypeBlock"),a=i.getSelectedElement());const r=s||e.createElement("caption");e.append(r,a),t&&e.setSelection(r,"in")}_hideImageCaption(e){const t=this.editor,i=t.model.document.selection,n=t.plugins.get("ImageCaptionEditing"),o=t.plugins.get("ImageCaptionUtils");let a,s=i.getSelectedElement();s?a=o.getCaptionFromImageModelElement(s):(a=o.getCaptionFromModelSelection(i),s=a.parent),n._saveCaption(s,a),e.setSelection(s,"on"),e.remove(a)}}class ne extends e.Plugin{static get requires(){return[h,te]}static get pluginName(){return"ImageCaptionEditing"}constructor(e){super(e),this._savedCaptionsMap=new WeakMap}init(){const e=this.editor,t=e.model.schema;t.isRegistered("caption")?t.extend("caption",{allowIn:"imageBlock"}):t.register("caption",{allowIn:"imageBlock",allowContentOf:"$block",isLimit:!0}),e.commands.add("toggleImageCaption",new ie(this.editor)),this._setupConversion(),this._setupImageTypeCommandsIntegration(),this._registerCaptionReconversion()}_setupConversion(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils"),a=e.t;e.conversion.for("upcast").elementToElement({view:e=>n.matchImageCaptionViewElement(e),model:"caption"}),e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>i.isBlockImage(e.parent)?t.createContainerElement("figcaption"):null}),e.conversion.for("editingDowncast").elementToElement({model:"caption",view:(e,{writer:n})=>{if(!i.isBlockImage(e.parent))return null;const s=n.createEditableElement("figcaption");n.setCustomProperty("imageCaption",!0,s),s.placeholder=a("Enter image caption"),(0,o.enablePlaceholder)({view:t,element:s,keepOnFocus:!0});const r=e.parent.getAttribute("alt"),c=r?a("Caption for image: %0",[r]):a("Caption for the image");return(0,l.toWidgetEditable)(s,n,{label:c})}})}_setupImageTypeCommandsIntegration(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.plugins.get("ImageCaptionUtils"),n=e.commands.get("imageTypeInline"),o=e.commands.get("imageTypeBlock"),a=e=>{if(!e.return)return;const{oldElement:n,newElement:o}=e.return;if(!n)return;if(t.isBlockImage(n)){const e=i.getCaptionFromImageModelElement(n);if(e)return void this._saveCaption(o,e)}const a=this._getSavedCaption(n);a&&this._saveCaption(o,a)};n&&this.listenTo(n,"execute",a,{priority:"low"}),o&&this.listenTo(o,"execute",a,{priority:"low"})}_getSavedCaption(e){const t=this._savedCaptionsMap.get(e);return t?o.Element.fromJSON(t):null}_saveCaption(e,t){this._savedCaptionsMap.set(e,t.toJSON())}_registerCaptionReconversion(){const e=this.editor,t=e.model,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils");t.document.on("change:data",(()=>{const o=t.document.differ.getChanges();for(const t of o){if("alt"!==t.attributeKey)continue;const o=t.range.start.nodeAfter;if(i.isBlockImage(o)){const t=n.getCaptionFromImageModelElement(o);if(!t)return;e.editing.reconvertItem(t)}}}))}}class oe extends e.Plugin{static get requires(){return[te]}static get pluginName(){return"ImageCaptionUI"}init(){const t=this.editor,i=t.editing.view,n=t.plugins.get("ImageCaptionUtils"),o=t.t;t.ui.componentFactory.add("toggleImageCaption",(a=>{const s=t.commands.get("toggleImageCaption"),r=new v.ButtonView(a);return r.set({icon:e.icons.caption,tooltip:!0,isToggleable:!0}),r.bind("isOn","isEnabled").to(s,"value","isEnabled"),r.bind("label").to(s,"value",(e=>o(e?"Toggle caption off":"Toggle caption on"))),this.listenTo(r,"execute",(()=>{t.execute("toggleImageCaption",{focusCaptionOnShow:!0});const e=n.getCaptionFromModelSelection(t.model.document.selection);if(e){const n=t.editing.mapper.toViewElement(e);i.scrollToTheSelection(),i.change((e=>{e.addClass("image__caption_highlighted",n)}))}t.editing.view.focus()})),r}))}}var ae=i(86),se={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(ae.Z,se);ae.Z.locals;class re extends e.Plugin{static get requires(){return[ne,oe]}static get pluginName(){return"ImageCaption"}}var le=i(448);function ce(e){const t=e.map((e=>e.replace("+","\\+")));return new RegExp(`^image\\/(${t.join("|")})$`)}function ge(e){return new Promise(((t,i)=>{const n=e.getAttribute("src");fetch(n).then((e=>e.blob())).then((e=>{const i=me(e,n),o=i.replace("image/",""),a=new File([e],`image.${o}`,{type:i});t(a)})).catch((e=>e&&"TypeError"===e.name?function(e){return function(e){return new Promise(((t,i)=>{const n=r.global.document.createElement("img");n.addEventListener("load",(()=>{const e=r.global.document.createElement("canvas");e.width=n.width,e.height=n.height;e.getContext("2d").drawImage(n,0,0),e.toBlob((e=>e?t(e):i()))})),n.addEventListener("error",(()=>i())),n.src=e}))}(e).then((t=>{const i=me(t,e),n=i.replace("image/","");return new File([t],`image.${n}`,{type:i})}))}(n).then(t).catch(i):i(e)))}))}function me(e,t){return e.type?e.type:t.match(/data:(image\/\w+);base64/)?t.match(/data:(image\/\w+);base64/)[1].toLowerCase():"image/jpeg"}class de extends e.Plugin{static get pluginName(){return"ImageUploadUI"}init(){const t=this.editor,i=t.t,n=n=>{const o=new le.FileDialogButtonView(n),a=t.commands.get("uploadImage"),s=t.config.get("image.upload.types"),r=ce(s);return o.set({acceptedType:s.map((e=>`image/${e}`)).join(","),allowMultipleFiles:!0,label:i("Upload image from computer"),icon:e.icons.imageUpload,tooltip:!0}),o.bind("isEnabled").to(a),o.on("done",((e,i)=>{const n=Array.from(i).filter((e=>r.test(e.type)));n.length&&(t.execute("uploadImage",{file:n}),t.editing.view.focus())})),o};if(t.ui.componentFactory.add("uploadImage",n),t.ui.componentFactory.add("imageUpload",n),t.plugins.has("ImageInsertUI")){const e=t.plugins.get("ImageInsertUI"),n=t.commands.get("uploadImage");e.registerIntegration({name:"upload",observable:n,buttonViewCreator:()=>{const n=t.ui.componentFactory.create("uploadImage");return n.bind("label").to(e,"isImageSelected",(e=>i(e?"Replace image from computer":"Upload image from computer"))),n},formViewCreator:()=>{const n=t.ui.componentFactory.create("uploadImage");return n.withText=!0,n.bind("label").to(e,"isImageSelected",(e=>i(e?"Replace from computer":"Upload from computer"))),n.on("execute",(()=>{e.dropdownView.isOpen=!1})),n}})}}}var ue=i(283),pe={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(ue.Z,pe);ue.Z.locals;var he=i(651),fe={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(he.Z,fe);he.Z.locals;var be=i(582),ke={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(be.Z,ke);be.Z.locals;class Ie extends e.Plugin{static get pluginName(){return"ImageUploadProgress"}constructor(e){super(e),this.uploadStatusChange=(e,t,i)=>{const n=this.editor,o=t.item,a=o.getAttribute("uploadId");if(!i.consumable.consume(t.item,e.name))return;const s=n.plugins.get("ImageUtils"),r=n.plugins.get(le.FileRepository),l=a?t.attributeNewValue:null,c=this.placeholder,g=n.editing.mapper.toViewElement(o),m=i.writer;if("reading"==l)return we(g,m),void ve(s,c,g,m);if("uploading"==l){const e=r.loaders.get(a);return we(g,m),void(e?(ye(g,m),function(e,t,i,n){const o=function(e){const t=e.createUIElement("div",{class:"ck-progress-bar"});return e.setCustomProperty("progressBar",!0,t),t}(t);t.insert(t.createPositionAt(e,"end"),o),i.on("change:uploadedPercent",((e,t,i)=>{n.change((e=>{e.setStyle("width",i+"%",o)}))}))}(g,m,e,n.editing.view),function(e,t,i,n){if(n.data){const o=e.findViewImgElement(t);i.setAttribute("src",n.data,o)}}(s,g,m,e)):ve(s,c,g,m))}"complete"==l&&r.loaders.get(a)&&function(e,t,i){const n=t.createUIElement("div",{class:"ck-image-upload-complete-icon"});t.insert(t.createPositionAt(e,"end"),n),setTimeout((()=>{i.change((e=>e.remove(e.createRangeOn(n))))}),3e3)}(g,m,n.editing.view),function(e,t){Ce(e,t,"progressBar")}(g,m),ye(g,m),function(e,t){t.removeClass("ck-appear",e)}(g,m)},this.placeholder="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="}init(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageBlock",this.uploadStatusChange),e.plugins.has("ImageInlineEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageInline",this.uploadStatusChange)}}function we(e,t){e.hasClass("ck-appear")||t.addClass("ck-appear",e)}function ve(e,t,i,n){i.hasClass("ck-image-upload-placeholder")||n.addClass("ck-image-upload-placeholder",i);const o=e.findViewImgElement(i);o.getAttribute("src")!==t&&n.setAttribute("src",t,o),_e(i,"placeholder")||n.insert(n.createPositionAfter(o),function(e){const t=e.createUIElement("div",{class:"ck-upload-placeholder-loader"});return e.setCustomProperty("placeholder",!0,t),t}(n))}function ye(e,t){e.hasClass("ck-image-upload-placeholder")&&t.removeClass("ck-image-upload-placeholder",e),Ce(e,t,"placeholder")}function _e(e,t){for(const i of e.getChildren())if(i.getCustomProperty(t))return i}function Ce(e,t,i){const n=_e(e,i);n&&t.remove(t.createRangeOn(n))}class Ee extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.model.document.selection.getSelectedElement();this.isEnabled=t.isImageAllowed()||t.isImage(i)}execute(e){const t=(0,r.toArray)(e.file),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((e,t)=>{const a=i.getSelectedElement();if(t&&a&&n.isImage(a)){const t=this.editor.model.createPositionAfter(a);this._uploadImage(e,o,t)}else this._uploadImage(e,o)}))}_uploadImage(e,t,i){const n=this.editor,o=n.plugins.get(le.FileRepository).createLoader(e),a=n.plugins.get("ImageUtils");o&&a.insertImage({...t,uploadId:o.id},i)}}class xe extends e.Plugin{static get requires(){return[le.FileRepository,v.Notification,t.ClipboardPipeline,h]}static get pluginName(){return"ImageUploadEditing"}constructor(e){super(e),e.config.define("image",{upload:{types:["jpeg","png","gif","bmp","webp","tiff"]}}),this._uploadImageElements=new Map}init(){const e=this.editor,t=e.model.document,i=e.conversion,n=e.plugins.get(le.FileRepository),a=e.plugins.get("ImageUtils"),s=e.plugins.get("ClipboardPipeline"),r=ce(e.config.get("image.upload.types")),l=new Ee(e);e.commands.add("uploadImage",l),e.commands.add("imageUpload",l),i.for("upcast").attributeToAttribute({view:{name:"img",key:"uploadId"},model:"uploadId"}),this.listenTo(e.editing.view.document,"clipboardInput",((t,i)=>{if(n=i.dataTransfer,Array.from(n.types).includes("text/html")&&""!==n.getData("text/html"))return;var n;const o=Array.from(i.dataTransfer.files).filter((e=>!!e&&r.test(e.type)));o.length&&(t.stop(),e.model.change((t=>{i.targetRanges&&t.setSelection(i.targetRanges.map((t=>e.editing.mapper.toModelRange(t)))),e.execute("uploadImage",{file:o})})))})),this.listenTo(s,"inputTransformation",((t,i)=>{const s=Array.from(e.editing.view.createRangeIn(i.content)).map((e=>e.item)).filter((e=>function(e,t){return!(!e.isInlineImageView(t)||!t.getAttribute("src")||!t.getAttribute("src").match(/^data:image\/\w+;base64,/g)&&!t.getAttribute("src").match(/^blob:/g))}(a,e)&&!e.getAttribute("uploadProcessed"))).map((e=>({promise:ge(e),imageElement:e})));if(!s.length)return;const r=new o.UpcastWriter(e.editing.view.document);for(const e of s){r.setAttribute("uploadProcessed",!0,e.imageElement);const t=n.createLoader(e.promise);t&&(r.setAttribute("src","",e.imageElement),r.setAttribute("uploadId",t.id,e.imageElement))}})),e.editing.view.document.on("dragover",((e,t)=>{t.preventDefault()})),t.on("change",(()=>{const i=t.differ.getChanges({includeChangesInGraveyard:!0}).reverse(),o=new Set;for(const t of i)if("insert"==t.type&&"$text"!=t.name){const i=t.position.nodeAfter,a="$graveyard"==t.position.root.rootName;for(const t of Se(e,i)){const e=t.getAttribute("uploadId");if(!e)continue;const i=n.loaders.get(e);i&&(a?o.has(e)||i.abort():(o.add(e),this._uploadImageElements.set(e,t),"idle"==i.status&&this._readAndUpload(i)))}}})),this.on("uploadComplete",((e,{imageElement:t,data:i})=>{const n=i.urls?i.urls:i;this.editor.model.change((e=>{e.setAttribute("src",n.default,t),this._parseAndSetSrcsetAttributeOnImage(n,t,e),a.setImageNaturalSizeAttributes(t)}))}),{priority:"low"})}afterInit(){const e=this.editor.model.schema;this.editor.plugins.has("ImageBlockEditing")&&e.extend("imageBlock",{allowAttributes:["uploadId","uploadStatus"]}),this.editor.plugins.has("ImageInlineEditing")&&e.extend("imageInline",{allowAttributes:["uploadId","uploadStatus"]})}_readAndUpload(e){const t=this.editor,i=t.model,n=t.locale.t,o=t.plugins.get(le.FileRepository),a=t.plugins.get(v.Notification),s=t.plugins.get("ImageUtils"),l=this._uploadImageElements;return i.enqueueChange({isUndoable:!1},(t=>{t.setAttribute("uploadStatus","reading",l.get(e.id))})),e.read().then((()=>{const n=e.upload(),o=l.get(e.id);if(r.env.isSafari){const e=t.editing.mapper.toViewElement(o),i=s.findViewImgElement(e);t.editing.view.once("render",(()=>{if(!i.parent)return;const e=t.editing.view.domConverter.mapViewToDom(i.parent);if(!e)return;const n=e.style.display;e.style.display="none",e._ckHack=e.offsetHeight,e.style.display=n}))}return i.enqueueChange({isUndoable:!1},(e=>{e.setAttribute("uploadStatus","uploading",o)})),n})).then((t=>{i.enqueueChange({isUndoable:!1},(i=>{const n=l.get(e.id);i.setAttribute("uploadStatus","complete",n),this.fire("uploadComplete",{data:t,imageElement:n})})),c()})).catch((t=>{if("error"!==e.status&&"aborted"!==e.status)throw t;"error"==e.status&&t&&a.showWarning(t,{title:n("Upload failed"),namespace:"upload"}),i.enqueueChange({isUndoable:!1},(t=>{t.remove(l.get(e.id))})),c()}));function c(){i.enqueueChange({isUndoable:!1},(t=>{const i=l.get(e.id);t.removeAttribute("uploadId",i),t.removeAttribute("uploadStatus",i),l.delete(e.id)})),o.destroyLoader(e)}}_parseAndSetSrcsetAttributeOnImage(e,t,i){let n=0;const o=Object.keys(e).filter((e=>{const t=parseInt(e,10);if(!isNaN(t))return n=Math.max(n,t),!0})).map((t=>`${e[t]} ${t}w`)).join(", ");if(""!=o){const e={srcset:o};t.hasAttribute("width")||t.hasAttribute("height")||(e.width=n),i.setAttributes(e,t)}}}function Se(e,t){const i=e.plugins.get("ImageUtils");return Array.from(e.model.createRangeOn(t)).filter((e=>i.isImage(e.item))).map((e=>e.item))}class Ae extends e.Plugin{static get pluginName(){return"ImageUpload"}static get requires(){return[xe,de,Ie]}}class Te extends v.View{constructor(e){super(e),this.set("imageURLInputValue",""),this.set("isImageSelected",!1),this.set("isEnabled",!0),this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this._focusables=new v.ViewCollection,this.focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.urlInputView=this._createUrlInputView(),this.insertButtonView=this._createInsertButton(),this.cancelButtonView=this._createCancelButton(),this._focusables.addMany([this.urlInputView,this.insertButtonView,this.cancelButtonView]),this.setTemplate({tag:"div",attributes:{class:["ck","ck-image-insert-url"]},children:[this.urlInputView,{tag:"div",attributes:{class:["ck","ck-image-insert-url__action-row"]},children:[this.insertButtonView,this.cancelButtonView]}]})}render(){super.render();for(const e of this._focusables)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createUrlInputView(){const e=this.locale,t=e.t,i=new v.LabeledFieldView(e,v.createLabeledInputText);return i.bind("label").to(this,"isImageSelected",(e=>t(e?"Update image URL":"Insert image via URL"))),i.bind("isEnabled").to(this),i.fieldView.placeholder="https://example.com/image.png",i.fieldView.bind("value").to(this,"imageURLInputValue",(e=>e||"")),i.fieldView.on("input",(()=>{this.imageURLInputValue=i.fieldView.element.value.trim()})),i}_createInsertButton(){const t=this.locale,i=t.t,n=new v.ButtonView(t);return n.set({icon:e.icons.check,class:"ck-button-save",type:"submit",withText:!0}),n.bind("label").to(this,"isImageSelected",(e=>i(e?"Update":"Insert"))),n.bind("isEnabled").to(this,"imageURLInputValue",this,"isEnabled",((...e)=>e.every((e=>e)))),n.delegate("execute").to(this,"submit"),n}_createCancelButton(){const t=this.locale,i=t.t,n=new v.ButtonView(t);return n.set({label:i("Cancel"),icon:e.icons.cancel,class:"ck-button-cancel",withText:!0}),n.bind("isEnabled").to(this),n.delegate("execute").to(this,"cancel"),n}focus(e){-1===e?this.focusCycler.focusLast():this.focusCycler.focusFirst()}}class Be extends e.Plugin{static get pluginName(){return"ImageInsertViaUrlUI"}static get requires(){return[K]}init(){this._imageInsertUI=this.editor.plugins.get("ImageInsertUI");const e=this.editor.commands.get("insertImage");this._imageInsertUI.registerIntegration({name:"url",observable:e,requiresForm:!0,buttonViewCreator:e=>this._createInsertUrlButton(e),formViewCreator:e=>this._createInsertUrlView(e)})}_createInsertUrlView(e){const t=this.editor,i=t.locale,n=i.t,o=t.commands.get("replaceImageSource"),a=t.commands.get("insertImage"),s=new Te(i),r=e?null:new v.CollapsibleView(i,[s]);return s.bind("isImageSelected").to(this._imageInsertUI),s.bind("isEnabled").toMany([a,o],"isEnabled",((...e)=>e.some((e=>e)))),s.imageURLInputValue=o.value||"",this._imageInsertUI.dropdownView.on("change:isOpen",(()=>{this._imageInsertUI.dropdownView.isOpen&&(s.imageURLInputValue=o.value||"",r&&(r.isCollapsed=!0))}),{priority:"low"}),s.on("submit",(()=>{o.isEnabled?t.execute("replaceImageSource",{source:s.imageURLInputValue}):t.execute("insertImage",{source:s.imageURLInputValue}),this._closePanel()})),s.on("cancel",(()=>this._closePanel())),r?(r.set({isCollapsed:!0}),r.bind("label").to(this._imageInsertUI,"isImageSelected",(e=>n(e?"Update image URL":"Insert image via URL"))),r):s}_createInsertUrlButton(t){const i=t?v.DropdownButtonView:v.ButtonView,n=this.editor,o=new i(n.locale),a=n.locale.t;return o.set({icon:e.icons.imageUrl,tooltip:!0}),o.bind("label").to(this._imageInsertUI,"isImageSelected",(e=>a(e?"Update image URL":"Insert image via URL"))),o}_closePanel(){this.editor.editing.view.focus(),this._imageInsertUI.dropdownView.isOpen=!1}}class Ve extends e.Plugin{static get pluginName(){return"ImageInsertViaUrl"}static get requires(){return[Be,K]}}class Ue extends e.Plugin{static get pluginName(){return"ImageInsert"}static get requires(){return[Ae,Ve,K]}}class ze extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageUtils").getClosestSelectedImageElement(e.model.document.selection);this.isEnabled=!!t,t&&t.hasAttribute("resizedWidth")?this.value={width:t.getAttribute("resizedWidth"),height:null}:this.value=null}execute(e){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection);this.value={width:e.width,height:null},o&&i.change((t=>{t.setAttribute("resizedWidth",e.width,o),t.removeAttribute("resizedHeight",o),n.setImageNaturalSizeAttributes(o)}))}}class Re extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageResizeEditing"}constructor(e){super(e),e.config.define("image",{resizeUnit:"%",resizeOptions:[{name:"resizeImage:original",value:null,icon:"original"},{name:"resizeImage:25",value:"25",icon:"small"},{name:"resizeImage:50",value:"50",icon:"medium"},{name:"resizeImage:75",value:"75",icon:"large"}]})}init(){const e=this.editor,t=new ze(e);this._registerSchema(),this._registerConverters("imageBlock"),this._registerConverters("imageInline"),e.commands.add("resizeImage",t),e.commands.add("imageResize",t)}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["resizedWidth","resizedHeight"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["resizedWidth","resizedHeight"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils");t.conversion.for("downcast").add((t=>t.on(`attribute:resizedWidth:${e}`,((e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=i.writer,o=i.mapper.toViewElement(t.item);null!==t.attributeNewValue?(n.setStyle("width",t.attributeNewValue,o),n.addClass("image_resized",o)):(n.removeStyle("width",o),n.removeClass("image_resized",o))})))),t.conversion.for("dataDowncast").attributeToAttribute({model:{name:e,key:"resizedHeight"},view:e=>({key:"style",value:{height:e}})}),t.conversion.for("editingDowncast").add((t=>t.on(`attribute:resizedHeight:${e}`,((t,n,o)=>{if(!o.consumable.consume(n.item,t.name))return;const a=o.writer,s=o.mapper.toViewElement(n.item),r="imageInline"===e?i.findViewImgElement(s):s;null!==n.attributeNewValue?a.setStyle("height",n.attributeNewValue,r):a.removeStyle("height",r)})))),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{width:/.+/}},model:{key:"resizedWidth",value:e=>u(e)?null:e.getStyle("width")}}),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{height:/.+/}},model:{key:"resizedHeight",value:e=>u(e)?null:e.getStyle("height")}})}}const Pe={small:e.icons.objectSizeSmall,medium:e.icons.objectSizeMedium,large:e.icons.objectSizeLarge,original:e.icons.objectSizeFull};class Ne extends e.Plugin{static get requires(){return[Re]}static get pluginName(){return"ImageResizeButtons"}constructor(e){super(e),this._resizeUnit=e.config.get("image.resizeUnit")}init(){const e=this.editor,t=e.config.get("image.resizeOptions"),i=e.commands.get("resizeImage");this.bind("isEnabled").to(i);for(const e of t)this._registerImageResizeButton(e);this._registerImageResizeDropdown(t)}_registerImageResizeButton(e){const t=this.editor,{name:i,value:n,icon:o}=e,a=n?n+this._resizeUnit:null;t.ui.componentFactory.add(i,(i=>{const n=new v.ButtonView(i),s=t.commands.get("resizeImage"),l=this._getOptionLabelValue(e,!0);if(!Pe[o])throw new r.CKEditorError("imageresizebuttons-missing-icon",t,e);return n.set({label:l,icon:Pe[o],tooltip:l,isToggleable:!0}),n.bind("isEnabled").to(this),n.bind("isOn").to(s,"value",Oe(a)),this.listenTo(n,"execute",(()=>{t.execute("resizeImage",{width:a})})),n}))}_registerImageResizeDropdown(e){const t=this.editor,i=t.t,n=e.find((e=>!e.value)),o=o=>{const a=t.commands.get("resizeImage"),s=(0,v.createDropdown)(o,v.DropdownButtonView),r=s.buttonView,l=i("Resize image");return r.set({tooltip:l,commandValue:n.value,icon:Pe.medium,isToggleable:!0,label:this._getOptionLabelValue(n),withText:!0,class:"ck-resize-image-button",ariaLabel:l,ariaLabelledBy:void 0}),r.bind("label").to(a,"value",(e=>e&&e.width?e.width:this._getOptionLabelValue(n))),s.bind("isEnabled").to(this),(0,v.addListToDropdown)(s,(()=>this._getResizeDropdownListItemDefinitions(e,a)),{ariaLabel:i("Image resize list"),role:"menu"}),this.listenTo(s,"execute",(e=>{t.execute(e.source.commandName,{width:e.source.commandValue}),t.editing.view.focus()})),s};t.ui.componentFactory.add("resizeImage",o),t.ui.componentFactory.add("imageResize",o)}_getOptionLabelValue(e,t=!1){const i=this.editor.t;return e.label?e.label:t?e.value?i("Resize image to %0",e.value+this._resizeUnit):i("Resize image to the original size"):e.value?e.value+this._resizeUnit:i("Original")}_getResizeDropdownListItemDefinitions(e,t){const i=new r.Collection;return e.map((e=>{const n=e.value?e.value+this._resizeUnit:null,o={type:"button",model:new v.Model({commandName:"resizeImage",commandValue:n,label:this._getOptionLabelValue(e),role:"menuitemradio",withText:!0,icon:null})};o.model.bind("isOn").to(t,"value",Oe(n)),i.add(o)})),i}}function Oe(e){return t=>null===e&&t===e||null!==t&&t.width===e}const Le="image_resized";class Fe extends e.Plugin{static get requires(){return[l.WidgetResize,h]}static get pluginName(){return"ImageResizeHandles"}init(){const e=this.editor.commands.get("resizeImage");this.bind("isEnabled").to(e),this._setupResizerCreator()}_setupResizerCreator(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils");t.addObserver(R),this.listenTo(t.document,"imageLoaded",((n,o)=>{if(!o.target.matches("figure.image.ck-widget > img,figure.image.ck-widget > picture > img,figure.image.ck-widget > a > img,figure.image.ck-widget > a > picture > img,span.image-inline.ck-widget > img,span.image-inline.ck-widget > picture > img"))return;const a=e.editing.view.domConverter,s=a.domToView(o.target),r=i.getImageWidgetFromImageView(s);let c=this.editor.plugins.get(l.WidgetResize).getResizerByViewElement(r);if(c)return void c.redraw();const g=e.editing.mapper,m=g.toModelElement(r);c=e.plugins.get(l.WidgetResize).attachTo({unit:e.config.get("image.resizeUnit"),modelElement:m,viewElement:r,editor:e,getHandleHost:e=>e.querySelector("img"),getResizeHost:()=>a.mapViewToDom(g.toViewElement(m.parent)),isCentered(){const e=m.getAttribute("imageStyle");return!e||"block"==e||"alignCenter"==e},onCommit(i){t.change((e=>{e.removeClass(Le,r)})),e.execute("resizeImage",{width:i})}}),c.on("updateSize",(()=>{r.hasClass(Le)||t.change((e=>{e.addClass(Le,r)}));const e="imageInline"===m.name?s:r;e.getStyle("height")&&t.change((t=>{t.removeStyle("height",e)}))})),c.bind("isEnabled").to(this)}))}}var De=i(968),je={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(De.Z,je);De.Z.locals;class We extends e.Plugin{static get requires(){return[Re,Fe,Ne]}static get pluginName(){return"ImageResize"}}class Me extends e.Command{constructor(e,t){super(e),this._defaultStyles={imageBlock:!1,imageInline:!1},this._styles=new Map(t.map((e=>{if(e.isDefault)for(const t of e.modelElements)this._defaultStyles[t]=e.name;return[e.name,e]})))}refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled?e.hasAttribute("imageStyle")?this.value=e.getAttribute("imageStyle"):this.value=this._defaultStyles[e.name]:this.value=!1}execute(e={}){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils");i.change((t=>{const o=e.value,{setImageSizes:a=!0}=e;let s=n.getClosestSelectedImageElement(i.document.selection);o&&this.shouldConvertImageType(o,s)&&(this.editor.execute(n.isBlockImage(s)?"imageTypeInline":"imageTypeBlock",{setImageSizes:a}),s=n.getClosestSelectedImageElement(i.document.selection)),!o||this._styles.get(o).isDefault?t.removeAttribute("imageStyle",s):t.setAttribute("imageStyle",o,s),a&&n.setImageNaturalSizeAttributes(s)}))}shouldConvertImageType(e,t){return!this._styles.get(e).modelElements.includes(t.name)}}const{objectFullWidth:qe,objectInline:$e,objectLeft:Ze,objectRight:He,objectCenter:Ke,objectBlockLeft:Ge,objectBlockRight:Je}=e.icons,Qe={get inline(){return{name:"inline",title:"In line",icon:$e,modelElements:["imageInline"],isDefault:!0}},get alignLeft(){return{name:"alignLeft",title:"Left aligned image",icon:Ze,modelElements:["imageBlock","imageInline"],className:"image-style-align-left"}},get alignBlockLeft(){return{name:"alignBlockLeft",title:"Left aligned image",icon:Ge,modelElements:["imageBlock"],className:"image-style-block-align-left"}},get alignCenter(){return{name:"alignCenter",title:"Centered image",icon:Ke,modelElements:["imageBlock"],className:"image-style-align-center"}},get alignRight(){return{name:"alignRight",title:"Right aligned image",icon:He,modelElements:["imageBlock","imageInline"],className:"image-style-align-right"}},get alignBlockRight(){return{name:"alignBlockRight",title:"Right aligned image",icon:Je,modelElements:["imageBlock"],className:"image-style-block-align-right"}},get block(){return{name:"block",title:"Centered image",icon:Ke,modelElements:["imageBlock"],isDefault:!0}},get side(){return{name:"side",title:"Side image",icon:He,modelElements:["imageBlock"],className:"image-style-side"}}},Xe={full:qe,left:Ge,right:Je,center:Ke,inlineLeft:Ze,inlineRight:He,inline:$e},Ye=[{name:"imageStyle:wrapText",title:"Wrap text",defaultItem:"imageStyle:alignLeft",items:["imageStyle:alignLeft","imageStyle:alignRight"]},{name:"imageStyle:breakText",title:"Break text",defaultItem:"imageStyle:block",items:["imageStyle:alignBlockLeft","imageStyle:block","imageStyle:alignBlockRight"]}];function et(e){(0,r.logWarning)("image-style-configuration-definition-invalid",e)}const tt={normalizeStyles:function(e){return(e.configuredStyles.options||[]).map((e=>function(e){e="string"==typeof e?Qe[e]?{...Qe[e]}:{name:e}:function(e,t){const i={...t};for(const n in e)Object.prototype.hasOwnProperty.call(t,n)||(i[n]=e[n]);return i}(Qe[e.name],e);"string"==typeof e.icon&&(e.icon=Xe[e.icon]||e.icon);return e}(e))).filter((t=>function(e,{isBlockPluginLoaded:t,isInlinePluginLoaded:i}){const{modelElements:n,name:o}=e;if(!(n&&n.length&&o))return et({style:e}),!1;{const o=[t?"imageBlock":null,i?"imageInline":null];if(!n.some((e=>o.includes(e))))return(0,r.logWarning)("image-style-missing-dependency",{style:e,missingPlugins:n.map((e=>"imageBlock"===e?"ImageBlockEditing":"ImageInlineEditing"))}),!1}return!0}(t,e)))},getDefaultStylesConfiguration:function(e,t){return e&&t?{options:["inline","alignLeft","alignRight","alignCenter","alignBlockLeft","alignBlockRight","block","side"]}:e?{options:["block","side"]}:t?{options:["inline","alignLeft","alignRight"]}:{}},getDefaultDropdownDefinitions:function(e){return e.has("ImageBlockEditing")&&e.has("ImageInlineEditing")?[...Ye]:[]},warnInvalidStyle:et,DEFAULT_OPTIONS:Qe,DEFAULT_ICONS:Xe,DEFAULT_DROPDOWN_DEFINITIONS:Ye};function it(e,t){for(const i of t)if(i.name===e)return i}class nt extends e.Plugin{static get pluginName(){return"ImageStyleEditing"}static get requires(){return[h]}init(){const{normalizeStyles:e,getDefaultStylesConfiguration:t}=tt,i=this.editor,n=i.plugins.has("ImageBlockEditing"),o=i.plugins.has("ImageInlineEditing");i.config.define("image.styles",t(n,o)),this.normalizedStyles=e({configuredStyles:i.config.get("image.styles"),isBlockPluginLoaded:n,isInlinePluginLoaded:o}),this._setupConversion(n,o),this._setupPostFixer(),i.commands.add("imageStyle",new Me(i,this.normalizedStyles))}_setupConversion(e,t){const i=this.editor,n=i.model.schema,o=(a=this.normalizedStyles,(e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=it(t.attributeNewValue,a),o=it(t.attributeOldValue,a),s=i.mapper.toViewElement(t.item),r=i.writer;o&&r.removeClass(o.className,s),n&&r.addClass(n.className,s)});var a;const s=function(e){const t={imageInline:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageInline"))),imageBlock:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageBlock")))};return(e,i,n)=>{if(!i.modelRange)return;const o=i.viewItem,a=(0,r.first)(i.modelRange.getItems());if(a&&n.schema.checkAttribute(a,"imageStyle"))for(const e of t[a.name])n.consumable.consume(o,{classes:e.className})&&n.writer.setAttribute("imageStyle",e.name,a)}}(this.normalizedStyles);i.editing.downcastDispatcher.on("attribute:imageStyle",o),i.data.downcastDispatcher.on("attribute:imageStyle",o),e&&(n.extend("imageBlock",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:figure",s,{priority:"low"})),t&&(n.extend("imageInline",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:img",s,{priority:"low"}))}_setupPostFixer(){const e=this.editor,t=e.model.document,i=e.plugins.get(h),n=new Map(this.normalizedStyles.map((e=>[e.name,e])));t.registerPostFixer((e=>{let o=!1;for(const a of t.differ.getChanges())if("insert"==a.type||"attribute"==a.type&&"imageStyle"==a.attributeKey){let t="insert"==a.type?a.position.nodeAfter:a.range.start.nodeAfter;if(t&&t.is("element","paragraph")&&t.childCount>0&&(t=t.getChild(0)),!i.isImage(t))continue;const s=t.getAttribute("imageStyle");if(!s)continue;const r=n.get(s);r&&r.modelElements.includes(t.name)||(e.removeAttribute("imageStyle",t),o=!0)}return o}))}}const ot=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const at=function(e){return e};var st=i(151),rt={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(st.Z,rt);st.Z.locals;class lt extends e.Plugin{static get requires(){return[nt]}static get pluginName(){return"ImageStyleUI"}get localizedDefaultStylesTitles(){const e=this.editor.t;return{"Wrap text":e("Wrap text"),"Break text":e("Break text"),"In line":e("In line"),"Full size image":e("Full size image"),"Side image":e("Side image"),"Left aligned image":e("Left aligned image"),"Centered image":e("Centered image"),"Right aligned image":e("Right aligned image")}}init(){const e=this.editor.plugins,t=this.editor.config.get("image.toolbar")||[],i=ct(e.get("ImageStyleEditing").normalizedStyles,this.localizedDefaultStylesTitles);for(const e of i)this._createButton(e);const n=ct([...t.filter(ot),...tt.getDefaultDropdownDefinitions(e)],this.localizedDefaultStylesTitles);for(const e of n)this._createDropdown(e,i)}_createDropdown(e,t){const i=this.editor.ui.componentFactory;i.add(e.name,(n=>{let o;const{defaultItem:a,items:s,title:r}=e,l=s.filter((e=>t.find((({name:t})=>gt(t)===e)))).map((e=>{const t=i.create(e);return e===a&&(o=t),t}));s.length!==l.length&&tt.warnInvalidStyle({dropdown:e});const c=(0,v.createDropdown)(n,v.SplitButtonView),g=c.buttonView,m=g.arrowView;return(0,v.addToolbarToDropdown)(c,l,{enableActiveItemFocusOnDropdownOpen:!0}),g.set({label:mt(r,o.label),class:null,tooltip:!0}),m.unbind("label"),m.set({label:r}),g.bind("icon").toMany(l,"isOn",((...e)=>{const t=e.findIndex(at);return t<0?o.icon:l[t].icon})),g.bind("label").toMany(l,"isOn",((...e)=>{const t=e.findIndex(at);return mt(r,t<0?o.label:l[t].label)})),g.bind("isOn").toMany(l,"isOn",((...e)=>e.some(at))),g.bind("class").toMany(l,"isOn",((...e)=>e.some(at)?"ck-splitbutton_flatten":void 0)),g.on("execute",(()=>{l.some((({isOn:e})=>e))?c.isOpen=!c.isOpen:o.fire("execute")})),c.bind("isEnabled").toMany(l,"isEnabled",((...e)=>e.some(at))),this.listenTo(c,"execute",(()=>{this.editor.editing.view.focus()})),c}))}_createButton(e){const t=e.name;this.editor.ui.componentFactory.add(gt(t),(i=>{const n=this.editor.commands.get("imageStyle"),o=new v.ButtonView(i);return o.set({label:e.title,icon:e.icon,tooltip:!0,isToggleable:!0}),o.bind("isEnabled").to(n,"isEnabled"),o.bind("isOn").to(n,"value",(e=>e===t)),o.on("execute",this._executeCommand.bind(this,t)),o}))}_executeCommand(e){this.editor.execute("imageStyle",{value:e}),this.editor.editing.view.focus()}}function ct(e,t){for(const i of e)t[i.title]&&(i.title=t[i.title]);return e}function gt(e){return`imageStyle:${e}`}function mt(e,t){return(e?e+": ":"")+t}class dt extends e.Plugin{static get requires(){return[nt,lt]}static get pluginName(){return"ImageStyle"}}class ut extends e.Plugin{static get requires(){return[l.WidgetToolbarRepository,h]}static get pluginName(){return"ImageToolbar"}afterInit(){const e=this.editor,t=e.t,i=e.plugins.get(l.WidgetToolbarRepository),n=e.plugins.get("ImageUtils");var o;i.register("image",{ariaLabel:t("Image toolbar"),items:(o=e.config.get("image.toolbar")||[],o.map((e=>ot(e)?e.name:e))),getRelatedElement:e=>n.getClosestSelectedImageWidget(e)})}}class pt extends e.Plugin{static get requires(){return[O,h]}static get pluginName(){return"PictureEditing"}afterInit(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.model.schema.extend("imageBlock",{allowAttributes:["sources"]}),e.plugins.has("ImageInlineEditing")&&e.model.schema.extend("imageInline",{allowAttributes:["sources"]}),this._setupConversion(),this._setupImageUploadEditingIntegration()}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("upcast").add(function(e){const t=["srcset","media","type","sizes"],i=(i,n,o)=>{const a=n.viewItem;if(!o.consumable.test(a,{name:!0}))return;const s=new Map;for(const e of a.getChildren())if(e.is("element","source")){const i={};for(const n of t)e.hasAttribute(n)&&o.consumable.test(e,{attributes:n})&&(i[n]=e.getAttribute(n));Object.keys(i).length&&s.set(e,i)}const l=e.findViewImgElement(a);if(!l)return;let c=n.modelCursor.parent;if(!c.is("element","imageBlock")){const e=o.convertItem(l,n.modelCursor);n.modelRange=e.modelRange,n.modelCursor=e.modelCursor,c=(0,r.first)(e.modelRange.getItems())}o.consumable.consume(a,{name:!0});for(const[e,t]of s)o.consumable.consume(e,{attributes:Object.keys(t)});s.size&&o.writer.setAttribute("sources",Array.from(s.values()),c),o.convertChildren(a,c)};return e=>{e.on("element:picture",i)}}(i)),t.for("downcast").add(function(e){const t=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a),r=i.attributeNewValue;if(r&&r.length){const e=o.createContainerElement("picture",null,r.map((e=>o.createEmptyElement("source",e)))),t=[];let i=s.parent;for(;i&&i.is("attributeElement");){const e=i.parent;o.unwrap(o.createRangeOn(s),i),t.unshift(i),i=e}o.insert(o.createPositionBefore(s),e),o.move(o.createRangeOn(s),o.createPositionAt(e,"end"));for(const i of t)o.wrap(o.createRangeOn(e),i)}else if(s.parent.is("element","picture")){const e=s.parent;o.move(o.createRangeOn(s),o.createPositionBefore(e)),o.remove(e)}};return e=>{e.on("attribute:sources:imageBlock",t),e.on("attribute:sources:imageInline",t)}}(i))}_setupImageUploadEditingIntegration(){const e=this.editor;if(!e.plugins.has("ImageUploadEditing"))return;const t=e.plugins.get("ImageUploadEditing");this.listenTo(t,"uploadComplete",((t,{imageElement:i,data:n})=>{const o=n.sources;o&&e.model.change((e=>{e.setAttributes({sources:o},i)}))}))}}})(),(window.CKEditor5=window.CKEditor5||{}).image=n})();