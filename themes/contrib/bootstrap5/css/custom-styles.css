
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');



.poppins-thin {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.poppins-extralight {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: normal;
}

.poppins-light {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.poppins-regular {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.poppins-medium {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.poppins-semibold {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: normal;
}

.poppins-bold {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.poppins-extrabold {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: normal;
}

.poppins-black {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: normal;
}

.poppins-thin-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: italic;
}

.poppins-extralight-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: italic;
}

.poppins-light-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.poppins-regular-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.poppins-medium-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.poppins-semibold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: italic;
}

.poppins-bold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: italic;
}

.poppins-extrabold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: italic;
}

.poppins-black-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: italic;
}



.rubik-light {
  font-family: "Rubik", sans-serif;
  font-optical-sizing: auto;
  font-weight: 300;
  font-style: normal;
}


h1, .heading-title {
    font-size: 56px;
    font-size: 3.1111111111111rem;
    font-family: 'Poppins',sans-serif;
    line-height: 1.1em;
    color:#fff;
}

h2 {
    font-size: 2.2222222222222rem;
    font-family: 'Poppins',sans-serif;
    line-height: 1.3em;
}

h3 {
    font-size: 1.7777777777778rem;
    font-family: 'Poppins',sans-serif;
    line-height: 1.4;
font-weight: 600;

}

h5 {
    font-size: 20px;
    font-family: 'Poppins',sans-serif;
    line-height: 1.4;
font-weight: 600;

}

p {
	 font-size: 18px;
    font-family: 'Rubik',sans-serif;
color: #7A7A7A;
margin-bottom: 1em;
}



.img-thumbnail {
	padding: 0.25rem;
	background-color: transparent;
	border: none;
	border-radius: var(--bs-border-radius);
	max-width: 100%;
	height: auto;
}




.closer-dot {
        position: relative;
        left: -10px; /* Adjust this value to move the dot closer or farther */
    }

 .list-item {
        font-size: 16px; /* Adjust the font size as needed */
        display: flex;
        align-items: baseline;
    }

 /* choix color */

.orange{color:#f29432;font-size: 5rem;}
.blu{color:#123C76}
.blufonce{color:#123C76}
.rouge{color:#f70000;font-size: 3rem;}
.vert{font-size: 3rem; color: #48c424;}


 /* faq */
 .faq-container {
    max-width: 600px;
    margin: 0 auto;
  }
  .faq-item {
    margin-bottom: 20px;
  }
  .faq-question {
    cursor: pointer;
    font-weight: bold;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: #f9f9f9;
  }
  .faq-answer {
    display: none;
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #fff;
  }




 /* comment */

.comment__about-text-formats {
    display: none;
}
#edit-field-construire-0-format {
	display: none;
}

.filter-guidelines-item.filter-guidelines-plain_text {
	display: none;
}

#edit-field-introduction-0-format-help-about,#edit-field-nommer-0-format-help-about,#edit-field-imager-0-format-help-about,#edit-field-proteger-0-format-help-about,#edit-field-valoriser-0-format-help-about,#edit-field-nommer-0-format-help-about {

	display: none;
}


.field.field--name-field-creafree-category.field--type-entity-reference.field--label-inline.clearfix {
	background: #2e586c;
	color: white;
}

.comment-x{
	background:#cae3f9;
margin:2em;
}

#edit-comment-body-und-0-format {
    display: none;
}



td.couleurorange{background:#f29432;color:#fff;}

.menu-item-last > a {
  color: orange !important;
}

.couleur2{background:#f29432;color:#fff;}

h1, h2, h3, h4, h5, h6 {
	clear: both;
}





.view-news .field--name-field-image img {
  width: 100%;
  height: auto; /* Maintient le ratio de l'image */
}

#webform-submission-contact-us-add-form,#webform-submission-contact-us-investors-add-form,#webform-submission-contact-us-incubators-add-form,#webform-submission-contact-us-counselors-add-form,#webform-submission-contact-us-experts-add-form,#webform-submission-contact-us-mediators-add-form{
	padding: 34px;
}


.js-form-item.form-item.js-form-type-email.form-type-email.js-form-item-email.form-item-email {
  color: #173d64;
}
.js-form-item.form-item.js-form-type-textfield.form-type-textfield.js-form-item-name.form-item-name {
  color: #173d64;
}
.js-form-item.form-item.js-form-type-textarea.form-type-textarea.js-form-item-message.form-item-message {
  color: #173d64;
}
.js-form-item.form-item.js-form-type-webform-document-file.form-type-webform-document-file.js-form-item-files.form-item-files {
   color: #173d64;
}

#edit-files-upload {
	color: black;
}
.form-type-webform-terms-of-service.js-form-type-webform-terms-of-service.js-form-item.form-item.js-form-type-checkbox.form-type-checkbox.js-form-item-terms-of-service.form-item-terms-of-service {
	color: black;
}



.webform-button--submit{text-align:center;}
#edit-files--description {
	display: none;
}

#edit-captcha-response--description {
	color: black;
}

.captcha__title.js-form-required.form-required {
	color: black;
}
.field-prefix {
	color: black;
}

.orangemenu{background:#FF8C2F;color:#fff;border-radius: 5%;}


.accordion {
	--bs-accordion-color: var(--bs-body-color);
	--bs-accordion-bg:  #F4F5F6;
	--bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
	--bs-accordion-border-color: var(--bs-border-color);
	--bs-accordion-border-width: var(--bs-border-width);
	--bs-accordion-border-radius: var(--bs-border-radius);
	--bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - (var(--bs-border-width)));
	--bs-accordion-btn-padding-x: 1.25rem;
	--bs-accordion-btn-padding-y: 1rem;
	--bs-accordion-btn-color: var(--bs-body-color);
	--bs-accordion-btn-bg: var(--bs-accordion-bg);
	--bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e");
	--bs-accordion-btn-icon-width: 1.25rem;
	--bs-accordion-btn-icon-transform: rotate(-180deg);
	--bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
	--bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23052c65' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e");
	--bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
	--bs-accordion-body-padding-x: 1.25rem;
	--bs-accordion-body-padding-y: 1rem;
	--bs-accordion-active-color: var(--bs-primary-text-emphasis);
	--bs-accordion-active-bg: var(--bs-primary-bg-subtle);
}





body, button, input, select, textarea, .ast-button, p {
	font-family: 'Rubik',sans-serif;
	font-weight: 400;
	font-size: 18px;
	font-size: 1rem;
}

.accordion-flush > .accordion-item > .accordion-header .accordion-button, .accordion-flush > .accordion-item > .accordion-header .accordion-button.collapsed {
	border-radius: 0;
	color: #123C76;
font-weight: 500;
}



.navbar-nav {
color:#fff;
	--bs-nav-link-padding-x: 0;
	--bs-nav-link-padding-y: 0.5rem;
	--bs-nav-link-font-weight: ;
	--bs-nav-link-color: var(--bs-navbar-color);
	--bs-nav-link-hover-color: #f29432 !important;
	--bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
	list-style: none;
}



table {
	caption-side: unset;
	background: white;
}

.view-empty{
    display: none;
}

.feed-icons {
    display: none;
}



.view-news .view-filters {

	align-items: center;
	justify-content: center;
}




/* Adjust margin between filter items */
.views-exposed-form .form-item > * {
    margin-right: 0.2rem; /* Adjust margin as needed */
}

.views-exposed-form .form-actions input[type="submit"] {

}








/* Style adjustments for inputs/selects */
.views-exposed-form select,
.views-exposed-form input {
  width: 100%; /* Ensure input fields take full width */
  box-sizing: border-box; /* Include padding and border in element's total width and height */
}

/* Style filter buttons to align properly */
.views-exposed-form .form-actions {
  display: flex;
  flex-shrink: 0; /* Prevent shrinking */
  margin-left: auto; /* Push buttons to the right */
}

/* Target the submit and reset buttons */
.views-exposed-form .form-actions .form-submit,
.views-exposed-form .form-actions .form-reset {
  width: auto; /* Ensure buttons size based on content */
  padding: 0.3em 0.8em; /* Adjust padding for better button appearance */
  font-size: 0.9em; /* Adjust font size for consistency */
}


/* Target the exposed filter form submit button */
.views-exposed-form .form-actions .form-submit {
  width: 50%; /* Set the button width to 50% of its default size */
  padding: 0.3em 0.5em; /* Adjust padding to scale down button size */
  font-size: 0.8em; /* Reduce font size to maintain proportion */
  box-sizing: border-box; /* Ensure the padding is included in width calculation */
}

/* Specifically target the autocomplete input field */
.views-exposed-form .form-autocomplete {
  width: 100%;
  max-width: 600px;
  min-width: 218px;
  box-sizing: border-box;
}


.buttons-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}


#block-bootstrap5-local-tasks {
	color: gray;
}

#block-bootstrap5-local-tasks .nav-item active{
	color: gray;
}


.column > *{
 flex:1;
flex-basis:0;
flex-shrink:1;
flex-grow:1;

}

/* dotnavigation non actif */
.list-group-item {

	border: 0 !important;
}



 .list-group-item {
            display: flex;
            align-items: flex-start;
        }
        .list-group-item-number {
            flex: 0 0 2rem; /* Ajuste cette valeur pour l'espace désiré */
        }
        .list-group-item-text {
            flex: 1;
        }


/* dotnavigation non actif */

.dot-navigation {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  z-index: 1000;
}

.dot-navigation ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dot-navigation ul li {
  margin: 10px 0;
}

.dot-navigation ul li a {
  display: block;
  width: 20px;
  height: 20px;
  background-color: #333;
  border-radius: 50%;
  text-indent: -9999px; /* Hide the text */
  transition: background-color 0.3s;
}

.dot-navigation ul li a.active {
  background-color: #666;
}

.dot-navigation a.active {
  animation: blink-expand 0.5s ease-in-out forwards;
}

@keyframes blink-expand {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}




.light-background {
  background-color: #f9f9f9;
  color: black;

}


/* Positioning and styling the tooltip */
.tooltip {
  position: absolute;
  background-color: #fff;
  color: #000;
  padding: 5px 10px;
  border-radius: 5px;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none; /* Prevent tooltip from being interactive */
}

/* contact form */

#edit-container-02 {
	margin-left: 150px;
	margin-right: 150px;
}




.webform-terms-of-service-details--content p {
	color: white;
}

/* menu */
.region.region-nav-main {
	padding-right: 50px;
}









/* Hover effect for dropdown items */
.dropdown-menu .dropdown-item:hover {
  color: #ffffff; /* Change text color on hover */
  background-color: #ed9e23; /* Orange background */
  transition: all 0.3s ease-in-out;
}

/* Styling for dropdown menu */
.dropdown-menu {
  border: 1px solid #ddd; /* Subtle border for dropdown */
  border-radius: 4px; /* Rounded corners */
  padding: 0.5rem; /* Space around items */
}





.navbar-brand a.site-title {
	color:#fff !important;
	text-decoration: none;
}






/* Example CSS to change the color of labels on the create account and user login forms */
.user-login-form label,
.user-pass label,.user-register-form label {
    color: #000; /* Replace with your desired color */
}

.nav.navbar-nav li.nav-item a {
	color: #fff;
}



.product-display .product-details {
  margin-top: 20px;
}

.product-display .product-description,
.product-display .product-price,
.product-display .add-to-cart {
  margin-bottom: 15px;
}


/* Ensure the field container uses flexbox */
.field--name-field-nice-number {
  display: flex; /* Use flexbox for layout */
  align-items: center; /* Center items vertically */
  gap: 1rem; /* Space between label and items */
  flex-wrap: nowrap; /* Prevent wrapping */
}

/* Field label styles */
.field--name-field-nice-number .field__label {
  font-weight: bold; /* Bold text for label */
  margin: 0; /* Remove default margin */
  padding-right: 0.1rem; /* Space between label and items */
}

/* Container for items */
.field--name-field-nice-number .field__items {
  display: flex; /* Use flexbox for item alignment */
  gap: 0.5rem; /* Space between items */
}

/* Individual field items */
.field--name-field-nice-number .field__item {
  padding: 0.5rem; /* Padding inside items */
  background-color: #f9f9f9; /* Background color for items */
  border: 0px solid #ddd; /* Border around items */
  border-radius: 4px; /* Rounded corners */
  text-align: center; /* Center text within items */
  white-space: nowrap; /* Prevent text from wrapping */
}

/* Optional: Responsive adjustments */
@media (max-width: 600px) {
  .field--name-field-nice-number {
    flex-direction: column; /* Stack label and items vertically on small screens */
    align-items: flex-start; /* Align items to the start */
  }

  .field--name-field-nice-number .field__label {
    padding-right: 0; /* Remove right padding on small screens */
    margin-bottom: 0.5rem; /* Space between label and items */
  }
}

/* Container for the field */
.field--name-field-certificate {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Field label style */
.field--name-field-certificate .field__label {
  font-weight: bold; /* Bold text for the label */
}

/* Container for field items */
.field--name-field-certificate .field__item {
  display: flex; /* Use flexbox to arrange stars horizontally */
  gap: 0.1rem; /* Space between stars */
}

/* Individual star styles */
.field--name-field-certificate .star {
  font-size: 1.5rem; /* Adjust size of the stars */
}

/* Specific star colors */
.field--name-field-certificate .star.gold {
  color: gold; /* Gold color for filled stars */
}

.field--name-field-certificate .star.gray {
  color: gray; /* Gray color for empty stars */
}




.field--name-body {
  display: block;
  padding: 0rem;
  width: 100%;
  box-sizing: border-box;
  min-height: 170px;
  text-align: justify;
}

/* Flex container for field item */
.field--name-body .field__item {
  display: block; /* Use block layout for field item */
  text-align: justify; /* Justify text within the item */
  max-width: 100%; /* Ensure the text does not exceed the container's width */
  box-sizing: border-box; /* Include padding and borders in the width */
  margin: 0 auto; /* Center the item horizontally, if necessary */
}






.nice-number-terms {
  display: flex;
  gap: 0.51rem;
  justify-content: flex-start;
  align-items: center;
  padding: 0px;
  color: #173d64;
}

.nice-number-term {
  border-radius: 4px;
  padding: 5px 10px;
  color: #f29432;
}

.square-rating.me-2 {
  color: #173d64;
}

.read-more-link {
  color: white;
  background: #f29432;
  text-decoration: none;
  width: 245px;
  padding: 7px;
}

/* Styling for the URC number field */
.field--name-field-creabook-identifier {
  flex-grow: 1; /* Allow the URC number to grow and fill available space */
  overflow-wrap: break-word; /* Ensure long text wraps within the container */
  max-width: 100%; /* Prevent the content from exceeding the container's width */
}



.node__read-more {
  text-align: center; /* Center the read more link/button */
  margin-top: 1em; /* Space above the read more link/button */
}

.node__read-more a {
  display: inline-block; /* Ensure the link is an inline block element */
  padding: 0.5em 1em; /* Add padding for better appearance */
  background-color: #007bff; /* Example background color */
  color: #fff; /* Text color */
  text-decoration: none; /* Remove underline */
  border-radius: 4px; /* Rounded corners */
}

.node__read-more a:hover {
  background-color: #0056b3; /* Darker background on hover */
}

.star-rating-container {
  position: relative;
  display: inline-block;
}

.star-description {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 5px;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  width: max-content;
  white-space: nowrap;
}

/* Container for centering the card */
.card-container {
  display: flex;
  justify-content: center; /* Center the card horizontally */
  align-items: center; /* Center the card vertically, if needed */
  min-height: 100vh; /* Full viewport height */
}

/* Card styling */
.card.custom-card {
  width: 50%; /* Adjust the width of the card */
  max-width: 460px; /* Set a maximum width */
  text-align: center; /* Center text inside the card */
  display: flex;
  flex-direction: column; /* Stack contents vertically */
  align-items: center; /* Center contents horizontally */
  padding: 1rem; /* Padding inside the card */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Optional: Add shadow for better visibility */
  margin: 1rem; /* Margin around the card if needed */
}

/* Logo styling */
.logo-img {
  width: 25%; /* Logo size relative to card width */
  height: auto; /* Maintain aspect ratio */
  margin-bottom: 1rem; /* Margin below the logo */
}

/* Card body padding */
.card-body {
  padding: 1.25rem; /* Adjust padding inside the card body */
}


.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--bs-body-color);
  appearance: none;
  background-color: var(--bs-body-bg);
  background-clip: padding-box;
  border: 1px solid #1b4582 !important;
  border-radius: var(--bs-border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}


.navbar-expand-lg {
  flex-wrap: nowrap;
  justify-content: flex-start;
  background: #fff;
  border-bottom: 1px solid #aaa;
  box-shadow: -2px 1px 3px 1px rgba(0, 0, 0, 0.3333);
}





.views-field.views-field-title {
  background: #1b4582;color:#fff;
  text-align: center;
  height:50px;

}

.views-field.views-field-title a::first-letter {
  text-transform: uppercase;
}

.views-field.views-field-title  a{
  color:#fff;
  text-decoration: none;
}



.views-field.views-field-field-source  a{
  text-decoration: none;

}


.views-view-responsive-grid__item {
  box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.5);
  padding: 0px;
  margin: 25px;
}


.views-field.views-field-field-image {

}

.views-field.views-field-field-source {
  padding: 13px;
}

ul.sf-menu .sf-with-ul {
  padding-right: 3em;
  color: #777272;
  font-size: 0.93em;
}



.couleur3{

  color: #173d64
}








 /* custon list */

.custom-list .list-group-item:last-child {
  border-bottom: none; /* Supprime la bordure du dernier élément */
}

.custom-list .list-group-item {
  border-bottom: 1px solid #a5a7aa !important;

}


li.list-group-item.pricesservice{
  color: orange;

}

.orange2{color:#f29432;}
.orange3{color:#f29432;font-size: 2.5rem}
.blue3{color:#274383}

.bi-x{size:22px;}


h4.titrebleu{
  color: whitesmoke;
  background:#173d64;
  padding-left: 0.85em;
}

li.list-group-item{
  color: #7A7A7A;

}


ul.menu {
  margin-left: 1em;
  padding: 0;
  list-style: none outside;
  text-align: left;
  text-transform: uppercase !important;
}


.subscribe-link.monpanier {
  color: #f29432 !important;
  text-align: right !important;
}





/* Ensure the last item aligns to the right */
ul.list-unstyled.list-group .couleurorange {
  text-align: right; /* Align content to the right */
  border-top: 1px solid orange; /* Add the top border for the button container */
  padding-top: 10px; /* Add spacing to separate the button from the border */
}

/* Style the button itself */
ul.list-unstyled.list-group .couleurorange a {
  display: inline-block; /* Ensure the link behaves like a button */
  background-color: orange; /* Set the background color */
  color: white; /* Set text color to white */
  border: none; /* Remove default border */
  padding: 8px 16px; /* Padding to make it look like a button */
  font-weight: bold; /* Bold text */
  text-decoration: none; /* Remove underline */
  border-radius: 4px; /* Optional: Rounded corners */
  transition: background-color 0.3s, color 0.3s; /* Smooth hover effect */
}

/* Hover effect for the button */
ul.list-unstyled.list-group .couleurorange a:hover {
  background-color: white; /* Change background to white */
  color: orange; /* Text color to orange */
  border: 1px solid orange; /* Add border on hover */
}





hr {
  margin: 0.03rem 0;
  color: inherit;
  border: 0;
  border-top: var(--bs-border-width) solid;
  opacity: 0.05;
}

.list-group {
  --bs-list-group-color: var(--bs-body-color);
  --bs-list-group-bg: var(--bs-body-bg);
  --bs-list-group-border-color: var(--bs-border-color);
  --bs-list-group-border-width: var(--bs-border-width);
  --bs-list-group-border-radius: var(--bs-border-radius);
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.2rem !important;
  --bs-list-group-action-color: var(--bs-secondary-color);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-tertiary-bg);
  --bs-list-group-action-active-color: var(--bs-body-color);
  --bs-list-group-action-active-bg: var(--bs-secondary-bg);
  --bs-list-group-disabled-color: var(--bs-secondary-color);
  --bs-list-group-disabled-bg: var(--bs-body-bg);
  --bs-list-group-active-color: #fff;
  --bs-list-group-active-bg: #0d6efd;
  --bs-list-group-active-border-color: #0d6efd;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius);
}

.fs-1 {
  font-size: 2.4rem !important;
}


.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.9rem;
  --bs-navbar-color: rgba(var(--bs-emphasis-color-rgb), 0.65);
  --bs-navbar-hover-color: rgba(var(--bs-emphasis-color-rgb), 0.8);
  --bs-navbar-disabled-color: rgba(var(--bs-emphasis-color-rgb), 0.3);
  --bs-navbar-active-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-padding-y: 0.3rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: -2.8rem;
  --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-hover-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-nav-link-padding-x: 1.8rem !important;
  --bs-navbar-toggler-padding-y: 1.3rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(var(--bs-emphasis-color-rgb), 0.15);
  --bs-navbar-toggler-border-radius: var(--bs-border-radius);
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
}

li a.nav-link {
  text-transform: uppercase;
  font-size: 0.85em;
}

ul.sf-menu .sf-sub-indicator::after {
  content: "▼";
  left: 0;
  line-height: 1;
  position: absolute;
  text-indent: 0;
  top: 0;
  color: orange !important;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  color: orange !important;
}

.nav-item {
  border-left: 1px solid #807d7d;
}


.fondbleu {
  background: #173d64;
  color: white;
  min-height: 402px;
  height: 725px;
}

.redcolor{color:#e11818;font-size:1.8em;font-weight: bold;}
.greencolor{color:#0ba20b;font-size:1.8em;}


.align-icon-text {
  display: flex;
  align-items: normal;
  font-size: 1.3em;
  line-height: 1.1em;
}

.icone2{font-size: 4.5em;}

.link.tabledrag-toggle-weight {
  display: none;
}





.bordure {
  border: 1px solid #bcbdbf;
  width: 17.8rem;
  margin: 4px auto;
  padding: 10px;
  box-sizing: border-box;
  box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.5);
 }

/* Ajouter un padding-top pour les trois premiers blocs */
.bordure:nth-child(-n+3) {
  padding-top: 20px; /* Ajustez la valeur selon vos besoins */
}

/* Media Query pour écrans moyens (tablettes) */
@media (max-width: 768px) {
  .bordure {
    width: 14rem;
    margin: 4px;
    padding: 8px;
  }
  .bordure:nth-child(-n+3) {
    padding-top: 15px;
  }
}

/* Media Query pour écrans petits (téléphones) */
@media (max-width: 576px) {
  .bordure {
    width: 100%;
    margin: 2px;
    padding: 6px;
  }
  .bordure:nth-child(-n+3) {
    padding-top: 10px;
  }
}

#creabook-slider {
  padding-top: 23px;
}




.col-md-4.col-sm-6.pt-5.fondbleu.text-light.creabook {
  background: #173d64;
  height: 1460px;
}


/* Media Query pour écrans petits (téléphones) */
@media (max-width: 576px) {
  .col-md-4.col-sm-6.pt-5.fondbleu.text-light.creabook {
   background: #173d64;
  height: 756px;
  }

}

.read-more {
  padding-bottom: 30px;
  text-align: center;
}

.nav-link.active.dropdown-toggle.is-active {
  color: #f29432
}


.article-columns {
  display: flex;
  gap: 20px;
}

.column {
  flex: 1;
}

.column-1 {
  max-width: 30%;
}

.column-2, .column-3 {
  max-width: 35%;
}
.article-title {
  font-size: 1.5em;
  margin-bottom: 10px;
  color:#7A7A7A;
}

.article-image img {
  max-width: 100%;
  height: auto;
  margin-bottom: 15px;
}

.article-body {
  font-size: 1em;
  line-height: 1.5;
}

.article-navigation {
  display: flex;
  justify-content: center; /* Center the buttons horizontally */
  gap: 20px; /* Adds spacing between the buttons */
  margin-top: 20px;
  padding: 10px 0;
  border-top: 1px solid #ddd;
}

.nav-arrow {
  text-decoration: none;
  font-size: 1.2rem;
  color: #007bff;
  padding: 10px 20px; /* Adds padding to make them look like buttons */
  border: 1px solid #007bff; /* Optional: Border for button style */
  border-radius: 5px; /* Optional: Rounded corners */
  transition: all 0.3s ease;
}

.nav-arrow:hover {
  color: #fff;
  background-color: #007bff;
}


.prev-arrow {
  margin-right: auto;
}

.next-arrow {
  margin-left: auto;
}


.image-wrapper img {
  width: 100%; /* Makes the image responsive */
  height: 200px; /* Set a fixed height */
  object-fit: cover; /* Ensures the image maintains aspect ratio while filling the space */
  display: block; /* Removes extra space below images */
}

.certificate-display {
  display: flex;
  gap: 5px;
}

.certificate-square {
  width: 20px;
  height: 20px;
  background-color: lightgray;
  border: 1px solid #ccc;
}

.certificate-square.filled {
  background-color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.certificate-square.empty {
  background-color: lightgray;
}


.square-rating-container {
  display: flex;
  align-items: center;
}

.square {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 2px;
  border: 1px solid #5caa30;
}

.square.filled {
  background-color: #028a02; /* Example filled color */
}

.square.empty {
  background-color: #fff; /* Example empty color */
}





.custom-file-upload {
  display: none; /* Hide the default file input */
}

.custom-file-upload-wrapper {
  display: block;
  background-color: #007bff; /* Background color for the block */
  color: #fff;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  font-size: 1rem;
  cursor: pointer;
  position: relative;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  border: none;
}

.custom-file-upload-wrapper:hover {
  background-color: #0056b3; /* Darker color on hover */
}

.custom-file-upload-wrapper .arrow-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
}

.custom-file-upload-wrapper input[type="file"] {
  display: none; /* Hide the input inside the wrapper */
}

/*.payment-method-separator {
  margin: 10px 0;
  border-top: 1px solid #ddd;
}

.payment-method-item {
  margin-bottom: 10px;
}

.payment-method-item label {
  margin-left: 5px;
  font-weight: bold;
}


.payment-method-separator hr {
  margin: 10px 0;
  border: 1px solid #ddd;
}


.payment-methods-container {
  margin: 20px 0;
}

.payment-method-section {
  margin-bottom: 20px;
}

.payment-method-section h3 {
  font-size: 1.2em;
  margin-bottom: 10px;
}


.payment-method-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  border-radius: 5px;
  border: 1px solid #b9b9b9;
  background: #173d64;
  color: white;
  transition: background 0.3s ease, transform 0.2s ease;
}

.payment-method-wrapper:hover {
  background: #0b498d; /* Slightly lighter shade for hover */
  transform: translateY(-5px); /* Subtle lift effect */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Add shadow on hover */
  cursor: pointer; /* Change cursor to pointer */
}*/



.payment-methods-wrapper {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}



.payment-methods-title {
  margin-bottom: 15px;
  font-size: 1.25rem;
  color: #333;
  border-bottom: 2px solid #173d64;
  display: inline-block;
  padding-bottom: 5px;
}

.payment-methods-container {
  margin-top: 10px;
}

.payment-method-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #b9b9b9;
  border-radius: 8px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.payment-method-wrapper:hover {
  background-color: #e6f7ff;
  border-color: #009ce0;
}

.payment-method-details h5 {
  margin: 0;
  font-size: 1rem;
  color: #173d64;
}

.payment-method-details p {
  margin: 5px 0 0;
  font-size: 0.9rem;
  color: #555;
}

.payment-method-input {
  margin: 0;
}
/* Full-Height Blue Sidebar */


.fondbleu2 {
  background: #173d64;
  color: white;
  min-height: 402px;
  height: 180ivh;
}



/*creafreetemplate creamakerbasic page*/
/* Ustawienia dla kontenera */
@media (min-width: 768px) {
  .creafree-layout {
    display: flex;
    align-items: flex-start;
  }

  .creafree-layout .sticky-left {
    position: sticky;
    top: 0;
    align-self: flex-start;
    height: 180vh;
    overflow: auto;
  }

  .creafree-layout .scrollable-right {
    overflow-y: auto;
    flex-grow: 1;
    padding-left: 1rem;
  }
}





.text-justify {
    text-align: justify;
  hyphens: auto;
  word-spacing: 0.05em; /* Optionnel : ajuste l'espacement entre les mots */
  text-justify: inter-word; 
   max-width: 700px;
  margin: auto;
}




.card-header.text-white.blue1 {
  background: #173d64;
}

/* Ensure uniformity for title, summary, tags, and read more */
.creabook-title {
    font-size: 1.2rem; /* Adjust as needed */
    font-weight: 600;
    text-transform: capitalize;
}

.creabook-summary {
    font-size: 1rem;
    color: #555;
}

.creabook-tags {
    font-size: 0.9rem;
    font-weight: 500;
    color: #777;
}

.creabook-item img {
    width: 100%; /* Ensures responsive images */
    height: auto;
    object-fit: cover;
}

.creabook-readmore {
    font-size: 1rem;
    font-weight: bold;
    display: inline-block;
    color: #007bff;
    text-decoration: none;
    margin-top: 10px;
}

.creabook-readmore:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .creabook-title {
        font-size: 1rem;
    }
    .creabook-summary, .creabook-tags, .creabook-readmore {
        font-size: 0.9rem;
    }
}


.image-full-cover img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }


    .info {
  font-size: clamp(0.99rem,3vh,5rem);
}

.display-5.fw-bold.blue, .mb-4.blue {
  color: #3857b3;
  font-size: 2.2em;
}

.h4.fw-bold,.display-6.fw-bold {
  color: #173d64;
}

.custom-title {

    margin-inline: auto;
    line-height: 1.3;
    word-break: break-word; /* Break words naturally if needed */
    color:#173d64;
  }

  @media (max-width: 768px) {
    .custom-title {

      color:#173d64;
    }
  }

  .contextual-region.product-wrapper.product-id- .field--name-body {
  text-align: left !important;
}

/* Creabook List View Styling for Drupal 10.2 */
.creabook-list-view .card {
  transition: transform 0.3s, box-shadow 0.3s;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
}

.creabook-list-view .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.creabook-list-view .card-img-container {
  height: 200px;
  width: 100%;
  overflow: hidden;
}

.creabook-list-view .card-img-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.creabook-list-view .card:hover .card-img-container img {
  transform: scale(1.05);
}

.creabook-list-view .creabook-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #173d64;
}

.creabook-list-view .author-image img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.creabook-list-view .creabook-summary {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.creabook-list-view .square-rating .square {
  display: inline-block;
  width: 15px;
  height: 15px;
  margin-right: 3px;
}

.creabook-list-view .square-rating .square.filled {
  background-color: #f29432;
}

.creabook-list-view .square-rating .square.empty {
  background-color: #e0e0e0;
}

.creabook-list-view .read-more-link {
  display: inline-block;
  background-color: #f29432;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s;
}

.creabook-list-view .read-more-link:hover {
  background-color: #e08321;
  text-decoration: none;
  color: white;
}

/* Pagination styling for Drupal 10.2 */
.creabook-list-view .pagination-container nav[aria-labelledby="pagination-heading"] ul.pagination {
  justify-content: center;
}

.creabook-list-view .pagination-container .page-item {
  margin: 0 3px;
}

.creabook-list-view .pagination-container .page-item .page-link {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  border: none;
  background-color: #f5f5f5;
}

.creabook-list-view .pagination-container .page-item.active .page-link {
  background-color: #173d64;
  color: white;
}

.creabook-list-view .pagination-container .page-item .page-link:hover {
  background-color: #e0e0e0;
}

/* Responsive adjustments for Drupal 10.2 */
@media (max-width: 767.98px) {
  .creabook-list-view .card-img-container {
    height: 180px;
  }

  .creabook-list-view .creabook-title {
    font-size: 1.1rem;
  }

  .creabook-list-view .pagination-container .page-item .page-link {
    width: 35px;
    height: 35px;
  }
}

/* Add border to all view items */
.views-row {

  margin-bottom: 1rem;
  border-radius: 0.375rem; /* Bootstrap's default border-radius */
}

/* If using grid or responsive grid layout */
.views-view-responsive-grid__item,
.views-view-grid .views-col {
  border: 1px solid #ccc;
  margin-bottom: 1rem;
  border-radius: 0.375rem;
}

/* For card-based views like the Creabook list */
.views-row .card {
  border: 1px solid #ccc !important;
}

/* Styles for the list of creabook view layout */
.view-list-of-creabook .sidebar-filter-column {
  background-color: #173d64;
  padding: 20px;
  border-radius: 5px;
}

.view-list-of-creabook .sidebar-filter-wrapper {
  position: sticky;
  top: 20px;
}

.view-list-of-creabook .form-filters {
  margin-bottom: 20px;
}

.view-list-of-creabook .filter-header {
  margin-bottom: 1.5rem;
}

.view-list-of-creabook .form-group label {
  color: white;
  font-weight: 500;
}

.view-list-of-creabook .form-actions .btn-warning {
  width: 100%;
  margin-bottom: 10px;
}

/* Card styles with border for all creabook items */
.view-list-of-creabook .card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #ccc;
  border-radius: 0.375rem;
  overflow: hidden;
  margin-bottom: 1rem;
}

.view-list-of-creabook .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* Center medium-sized images in the creabook view */
.view-list-of-creabook .card-img-container {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  height: 200px; /* Medium size height */
  padding: 10px;
}

.view-list-of-creabook .card-img-container img,
.view-list-of-creabook .views-field-field-image img,
.view-list-of-creabook .field--name-field-image img {
  width: auto;
  height: auto;
  max-width: 180px; /* Medium size width */
  max-height: 180px; /* Medium size height */
  object-fit: contain;
  object-position: center;
  margin: 0 auto;
}

/* Ensure all images have the same dimensions */
.view-list-of-creabook .image-style-medium {
  width: auto;
  height: auto;
  max-width: 180px;
  max-height: 180px;
}

/* Exposed filter background styling */
.view-list-of-creabook .views-exposed-form,
.sidebar-filter {
  background-color: #173d64;

}

/* Ensure filter text is visible on dark background */
.view-list-of-creabook .views-exposed-form label,
.view-list-of-creabook .views-exposed-form .form-item,
.view-list-of-creabook .views-exposed-form .form-actions {
  color: white;
}

/* Style filter buttons to stand out */
.view-list-of-creabook .views-exposed-form .form-actions .btn-warning {
  background-color: #f29432;
  border-color: #f29432;
  color: white;
  font-weight: 500;
}

.view-list-of-creabook .views-exposed-form .form-actions .btn-warning:hover {
  background-color: #e58a25;
  border-color: #e58a25;
}

/* Pagination styling for Drupal 10.2 */
.view-list-of-creabook .pagination-container nav[aria-labelledby="pagination-heading"] ul.pagination {
  justify-content: center;
  margin-top: 2rem;
}

.view-list-of-creabook .pagination-container .page-item {
  margin: 0 3px;
}

.view-list-of-creabook .pagination-container .page-item .page-link {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  border: 1px solid #dee2e6;
  background-color: #fff;
}

.view-list-of-creabook .pagination-container .page-item.active .page-link {
  background-color: #173d64;
  color: white;
  border-color: #173d64;
}

.view-list-of-creabook .pagination-container .page-item .page-link:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

/* Responsive adjustments for pagination */
@media (max-width: 767.98px) {
  .view-list-of-creabook .pagination-container .page-item .page-link {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
}

/* Center images in Drupal 10.2 field structure */
.view-list-of-creabook .field--name-field-image,
.view-list-of-creabook .field--type-image {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  text-align: center;
}

/* Target the image container in Drupal 10.2 */
.view-list-of-creabook .field--name-field-image .field__item,
.view-list-of-creabook .field--type-image .field__item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Target the actual image element */
.view-list-of-creabook .field--name-field-image img,
.view-list-of-creabook .field--type-image img {
  width: auto;
  height: auto;
  max-width: 180px;
  max-height: 180px;
  object-fit: contain;
  margin: 0 auto;
}

/* Target responsive image setup in Drupal 10.2 */
.view-list-of-creabook .field--name-field-image picture,
.view-list-of-creabook .field--type-image picture {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Target image styles specifically */
.view-list-of-creabook img.image-style-medium {
  display: block;
  margin: 0 auto;
}

/* Add specific targeting for Views image fields */
.view-list-of-creabook .views-field-field-image {
  text-align: center;
}

.view-list-of-creabook .views-field-field-image .field-content {
  display: flex;
  justify-content: center;
}

/* Specifically target field_image in views to ensure centering */
.view-list-of-creabook .views-field-field-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;
  padding: 10px 0;
}

.view-list-of-creabook .views-field-field-image .field-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.view-list-of-creabook .views-field-field-image img {
  width: auto;
  height: auto;
  max-width: 180px;
  max-height: 180px;
  object-fit: contain;
  margin: 0 auto;
}

/* Target the image wrapper if present */
.view-list-of-creabook .views-field-field-image .image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Target any anchor tags that might wrap images */
.view-list-of-creabook .views-field-field-image a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Ensure consistent height for image container */
.view-list-of-creabook .views-field-field-image {
  height: 200px;
  overflow: hidden;
}

/* Add border to node-creabook teaser */
.node--type-creabook.node--view-mode-teaser {
  border: 1px solid #ccc;
  border-radius: 0.375rem;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.node--type-creabook.node--view-mode-teaser:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Ensure proper spacing within the teaser */
.node--type-creabook.node--view-mode-teaser .node__content {
  padding: 10px 0;
}

/* Ensure image is centered in teaser view */
.node--type-creabook.node--view-mode-teaser .field--name-field-image {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.node--type-creabook.node--view-mode-teaser .field--name-field-image img {
  max-width: 180px;
  max-height: 180px;
  object-fit: contain;
}

/* Styling for the left block in node.379 inspired by views exposed news */
.node--379 .fondbleu {
  background-color: #173d64;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  padding: 20px;
  margin-bottom: 20px;
}

/* Style for the heading with orange square icon */
.node--379 .fondbleu h1 {
  color: white;
  font-weight: bold;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Style for the descriptive text */
.node--379 .fondbleu p {
  color: white;
  font-size: 1.25rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

/* Add border and shadow to the entire section */
.node--379 #wcs,
.node--379 #CreaFREEEcosystem {
  border: 1px solid #ccc;
  border-radius: 0.375rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

/* Style for the right column content */
.node--379 .col-md-8 {
  padding: 20px;
}

/* Style for the list items with icons */
.node--379 .align-icon-text {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.node--379 .align-icon-text i.bi-arrow-right-circle-fill {
  color: #f29432;
  font-size: 1.1rem;
  margin-top: 0.2rem;
}

/* Style for section headings */
.node--379 h4.fw-bold {
  color: #173d64;
  margin-bottom: 1rem;
  text-align: center;
}

/* Style for the orange icons */
.node--379 .icone2.orange2 {
  color: #f29432;
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Style for the date filter in views exposed form */
.views-exposed-form .date-filter-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.views-exposed-form .date-range-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.views-exposed-form .date-field {
  display: flex;
  flex-direction: column;
}

.views-exposed-form .date-label {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

/* Ensure date inputs have consistent styling */
.views-exposed-form input[type="date"],
.views-exposed-form .form-date {
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Improve mobile responsiveness for date fields */
@media (min-width: 768px) {
  .views-exposed-form .date-range-wrapper {
    flex-direction: row;
    gap: 15px;
  }

  .views-exposed-form .date-field {
    flex: 1;
  }
}

/* Enhanced styling for date range filters */
.views-exposed-form .date-filter-container {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 10px;
}

.views-exposed-form .date-range-wrapper {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.views-exposed-form .date-field {
  position: relative;
}

.views-exposed-form .date-label {
  display: block;
  font-weight: 500;
  color: #f0f0f0;
  margin-bottom: 5px;
}

.views-exposed-form input[type="date"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
}

.views-exposed-form input[type="date"]:focus {
  outline: none;
  border-color: #f29432;
  box-shadow: 0 0 0 2px rgba(242, 148, 50, 0.25);
}

/* Date input calendar icon styling */
.views-exposed-form input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
  opacity: 0.7;
}

/* Responsive layout for larger screens */
@media (min-width: 768px) {
  .views-exposed-form .date-range-wrapper {
    flex-direction: row;
  }

  .views-exposed-form .date-field {
    flex: 1;
  }
}

/* Add a subtle divider between from/to dates on mobile */
@media (max-width: 767px) {
  .views-exposed-form .date-field:first-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
  }
}

/* Styling for date filters with CCYY-MM-DD format */
.views-exposed-form .date-filter-container {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 10px;
}

.views-exposed-form .date-range-wrapper {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.views-exposed-form .date-field {
  position: relative;
}

.views-exposed-form .date-label {
  display: block;
  font-weight: 500;
  color: #f0f0f0;
  margin-bottom: 5px;
}

/* Style for text inputs with date format */
.views-exposed-form input[type="text"].form-date,
.views-exposed-form input[type="text"].form-text {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  font-family: monospace; /* Better for date formats */
}

/* Add placeholder styling */
.views-exposed-form input[type="text"].form-date::placeholder,
.views-exposed-form input[type="text"].form-text::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.views-exposed-form input[type="text"].form-date:focus,
.views-exposed-form input[type="text"].form-text:focus {
  outline: none;
  border-color: #f29432;
  box-shadow: 0 0 0 2px rgba(242, 148, 50, 0.25);
}

/* Format hint below the input */
.views-exposed-form .date-field .format-hint {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

/* Responsive layout for larger screens */
@media (min-width: 768px) {
  .views-exposed-form .date-range-wrapper {
    flex-direction: row;
  }

  .views-exposed-form .date-field {
    flex: 1;
  }
}

/* Add a subtle divider between from/to dates on mobile */
@media (max-width: 767px) {
  .views-exposed-form .date-field:first-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
  }
}


.fondbleu3 {
  background: #173d64;
  color: white;
  min-height: 552px;
  height: 121vh;
}




  .form-check-input {
 border:1px solid #1b4582 !important
     
}

.display-6.blue {
  color: #0e323e;
}