{#
/**
 * @file
 * Theme override to display a node.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - node.getCreatedTime() will return the node creation timestamp.
 *   - node.hasField('field_example') returns TRUE if the node bundle includes
 *     field_example. (This does not indicate the presence of a value in this
 *     field.)
 *   - node.isPublished() will return whether the node is published or not.
 *   Calling other methods, such as node.delete(), will result in an exception.
 *   See \Drupal\node\Entity\Node for a full list of public properties and
 *   methods for the node object.
 * - label: (optional) The title of the node.
 * - content: All node items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - author_picture: The node author user entity, rendered using the "compact"
 *   view mode.
 * - metadata: Metadata for this node.
 * - date: (optional) Themed creation date field.
 * - author_name: (optional) Themed author name field.
 * - url: Direct URL of the current node.
 * - display_submitted: Whether submission information should be displayed.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - node: The current template type (also known as a "theming hook").
 *   - node--type-[type]: The current node type. For example, if the node is an
 *     "Article" it would result in "node--type-article". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - node--view-mode-[view_mode]: The View Mode of the node; for example, a
 *     teaser would result in: "node--view-mode-teaser", and
 *     full: "node--view-mode-full".
 *   The following are controlled through the node publishing options.
 *   - node--promoted: Appears on nodes promoted to the front page.
 *   - node--sticky: Appears on nodes ordered above other non-sticky nodes in
 *     teaser listings.
 *   - node--unpublished: Appears on unpublished nodes visible only to site
 *     admins.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main
 *   content tag that appears in the template.
 * - author_attributes: Same as attributes, except applied to the author of
 *   the node tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - teaser: Flag for the teaser state. Will be true if view_mode is 'teaser'.
 * - page: Flag for the full page state. Will be true if view_mode is 'full'.
 * - readmore: Flag for more state. Will be true if the teaser content of the
 *   node cannot hold the main body content.
 * - logged_in: Flag for authenticated user status. Will be true when the
 *   current user is a logged-in member.
 * - is_admin: Flag for admin user status. Will be true when the current user
 *   is an administrator.
 *
 * @see template_preprocess_node()
 *
 */
#}

{%
  set classes = [
    'node',
    'node--type-' ~ node.bundle|clean_class,
    node.isPromoted() ? 'node--promoted',
    node.isSticky() ? 'node--sticky',
    not node.isPublished() ? 'node--unpublished',
    view_mode ? 'node--view-mode-' ~ view_mode|clean_class,
  ]
%}
{{ attach_library('bootstrap5/node') }}
<article{{attributes.addClass(classes)}}>

	{{ title_prefix }}
	{% if label and not page %}
		<h2{{title_attributes}}>
			<a href="{{ url }}" rel="bookmark">{{ label }}</a>
		</h2>
	{% endif %}
	{{ title_suffix }}

	{% if display_submitted %}
		<footer class="node__meta">
			{{ author_picture }}
			<div{{author_attributes.addClass('node__submitted')}}>
				{% trans %}Submitted by
				{{ author_name }}
				on
				{{ date }}{% endtrans %}
				{{ metadata }}
			</div>
		</footer>
	{% endif %}

	<div{{content_attributes.addClass('node__content')}}>
		{{ content }}
		<!-- creacorn -->

		<div class="container-fluid m-0 text-dark creafree-layout">
			<div
				class="row">
				<!-- 1/3 Left Section for CreaMAKER Download and Description -->
				<div class="col-md-4 col-sm-12 pt-5 fondbleu2 sticky-left">
					<div class="container text-dark text-start">
						<div class="row">
							<h1 class="fs-1 text-start text-light fw-bold">
								<svg class="bi bi-square-fill" xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#f29432" viewbox="0 0 16 16">
									<path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2z"></path>
								</svg>
								BASIC CreaMAKER
							</h1>
							<p class="text-light fs-4 text-start mx-3 p-4 pt-2">
								Private patent protecting<br>
your innovation at zero cost<br>
Learn how to make a Basic CreaBOOK
							</p>
							<ul class="text-light fs-4 text-start p-4 pt-2" style="list-style-type: square;"><li class="text-light fs-6 text-start mx-3">Free step by step guide to draft
your private patent</li>
<li class="text-light fs-6 text-start mx-3">Ensure your return on investment
through a long term and world wide protection</li>
<li class="text-light fs-6 text-start mx-3 ">Prove the primacy of your authorship
through timestamping</li>

</ul>

<p class="text-center">
<img loading="lazy" src="https://creafree.org/imagecreamakerbasic.png" width="291" height="336" alt="creamakerbasic"></p>
<p class="text-center">
<a href="/form/creamaker-basic" class="btn btn-primary btn-lg" role="button" aria-pressed="true">Download Now</a>
</p>



						</div>
					</div>
				</div>
				<!-- 2/3 Right Section with Lists and Text Block -->
				<div class="col-md-8 col-sm-12 pt-2 scrollable-right">
    <div class="px-4 py-3">
        <div class="mb-4">
            <div class="clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item">
                <div class="container py-2">
                    <div class="mb-5">
                        <h1 class="text-start fw-bold display-6 custom-title">
                            Basic CreaMAKER <br>The essential tool to protect<br>
and showcase your innovation - <br>without traditional patents
                        </h1>
                       
                    </div>
                    <section class="mb-5">
                         <h2 class="h4 fw-bold">
                            Content and Purpose
                        </h2>
                        <div class="service-item mb-4">
                           <p>BASIC CreaMAKER is a simple and structured private patent compatible with the CreaFREE Standard. It allows any creator to formalize his creative idea, prove its originality and timetamp its primacy</p>
                        </div>
                        <div class="service-item mb-4">
                            <p>
                                This private patent is made for entrepreneurs, startup founders, students, searchers, inventors, artists, engineers, companies, NGOs, local authorities — anyone who innovates and needs IP without the costs, delays and complexities of the traditional state patents.
                            </p>
                        </div>
                        <div class="service-item mb-4">
                            <p>
                                In order to finance the necessary research and development required to bring your innovation to market, your startup must obtain the support of venture capitalists. This support depends on the return on the investment (ROI) you can offer. To guarantee ROI, your startup needs efficient intellectual property protecting its innovation.
                            </p>
                        </div>
                         <div class="service-item mb-4">
                            <p>
                                With this new tool, your startup can register its private patent and immediately disclose its protected content to share its attractive return with partners and investors.
                            </p>
                        </div>
                    </section>
                </div>
                  <section class="mb-5">
                    <h2 class="h4 fw-bold">
                        What Will you Get?
                    </h2>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Tutorial:</u></strong> Guide to design your CreaBOOK.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Free download:</u></strong> Access to the tutorial at zero cost.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Step-by-step guide:</u></strong> Describe and prove the authenticity of your claims.
                        </p>
                    </div>
                     <div class="service-item mb-4">
                        <p>
                            <strong><u>Primacy proof:</u></strong> Timestamp your CreaBOOK with the provided tool and establish the primacy of your author rights.
                        </p>
                    </div>
                </section>
                <section class="mb-5">
                    <h2 class="h4 fw-bold">
                       Why is CreaBOOK Protection Unique?
                    </h2>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Competitive Advantage:</u></strong> The private patent protects all your creative ideas against unfair competitors.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Global market:</u></strong> Anchors your startup property worldwide.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Attract Investors:</u></strong> Long term protection which lasts during all your life plus at least 50 years after your death.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Free, fast and easy to use:</u></strong> Your first CreaBOOK can be written, registered and published within a few hours.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Flexible:</u></strong> Can be upgraded with the assistance of accredited IP coaches.
                        </p>
                    </div>
                    <div class="service-item mb-4">
                        <p>
                            <strong><u>Certified:</u></strong> Once registered and upgraded as GOLD CreaBOOK, the conformity, value and originality of your CreaBOOK can be certified by World creators Society.
                        </p>
                    </div>
                </section>
                <section class="mb-5">
                    <h2 class="h4 fw-bold">
                        How to Proceed?
                    </h2>
                    <div class="steps-container py-3">
                        <div class="step-item mb-4">
                            <p>
                                <strong>1.</strong> <u>Download the Tutorial:</u> Click on the blue button at the end of the blue column.
                            </p>
                        </div>
                        <div class="step-item mb-4">
                            <p>
                                <strong>2.</strong> <u>Make a copy of the tutorial file:</u> Work offline in complete confidentiality before publishing.
                            </p>
                        </div>
                        <div class="step-item mb-4">
                            <p>
                                <strong>3.</strong> <u>Refer to documentary evidence:</u> List all your primacy evidence in annexes (sketches, articles, mails, photos, crypted know-how …) and timestamp each of them.
                            </p>
                        </div>
                        <div class="step-item mb-4">
                            <p>
                                <strong>4.</strong> <u>Establish the primacy of your primacy:</u> Timestamp your private patent which includes the list of annexes.
                            </p>
                        </div>
                    </div>
                </section>
                <section class="mb-5">
                    <h2 class="h4 fw-bold">
                        Next Step: Publish Your CreaBOOK
                    </h2>
                    <p>
                        To assert your claims and benefit from the other services of the CreaFREE ecosystem, publish your CreaBOOK in the World creations Register.
                    </p>
                    
                </section>
            </div>
        </div>
    </div>
</div>
		</div>
	</article>
